from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import Permission
from accounts.models import Role, UserRole


class RoleBasedPermissionBackend(ModelBackend):
    """
    Custom authentication backend that combines Django permissions with role-based permissions
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        # Use the default authentication
        return super().authenticate(request, username, password, **kwargs)
    
    def has_perm(self, user_obj, perm, obj=None):
        """
        Check if user has permission through roles or default Django permissions
        """
        if not user_obj.is_active:
            return False
        
        # Check default Django permissions first
        if super().has_perm(user_obj, perm, obj):
            return True
        
        # Check role-based permissions
        return self.has_role_permission(user_obj, perm)
    
    def has_role_permission(self, user_obj, perm):
        """
        Check if user has permission through assigned roles
        """
        try:
            # Get permission object
            app_label, codename = perm.split('.')
            permission = Permission.objects.get(
                codename=codename,
                content_type__app_label=app_label
            )
            
            # Check if user has this permission through any active role
            return UserRole.objects.filter(
                user=user_obj,
                is_active=True,
                role__is_active=True,
                role__permissions=permission
            ).exists()
            
        except (Permission.DoesNotExist, ValueError):
            return False
    
    def get_user_permissions(self, user_obj, obj=None):
        """
        Get all permissions for user including role-based permissions
        """
        permissions = super().get_user_permissions(user_obj, obj)
        
        # Add role-based permissions
        role_permissions = user_obj.get_role_permissions()
        role_permission_codes = set()
        
        for perm in role_permissions:
            perm_code = f"{perm.content_type.app_label}.{perm.codename}"
            role_permission_codes.add(perm_code)
        
        return permissions | role_permission_codes
    
    def get_group_permissions(self, user_obj, obj=None):
        """
        Get group permissions (including role-based permissions)
        """
        permissions = super().get_group_permissions(user_obj, obj)
        
        # Add role-based permissions as "group" permissions
        role_permissions = user_obj.get_role_permissions()
        role_permission_codes = set()
        
        for perm in role_permissions:
            perm_code = f"{perm.content_type.app_label}.{perm.codename}"
            role_permission_codes.add(perm_code)
        
        return permissions | role_permission_codes
    
    def get_all_permissions(self, user_obj, obj=None):
        """
        Get all permissions for user (Django + role-based)
        """
        if not user_obj.is_active or user_obj.is_anonymous:
            return set()
        
        # Get default permissions
        permissions = super().get_all_permissions(user_obj, obj)
        
        # Add role-based permissions
        role_permissions = user_obj.get_role_permissions()
        for perm in role_permissions:
            perm_code = f"{perm.content_type.app_label}.{perm.codename}"
            permissions.add(perm_code)
        
        return permissions
