from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from accounts.models import Role


class Command(BaseCommand):
    help = 'Create default roles for admin panel'

    def handle(self, *args, **options):
        """Create default roles with appropriate permissions"""
        
        # Define default roles and their permissions
        default_roles = {
            'Super Admin': {
                'description': 'Full access to all admin features',
                'permissions': ['*']  # All permissions
            },
            'User Manager': {
                'description': 'Manage users, talents, and brands',
                'permissions': [
                    'accounts.view_user',
                    'accounts.add_user',
                    'accounts.change_user',
                    'accounts.delete_user',
                    'accounts.view_talentprofile',
                    'accounts.add_talentprofile',
                    'accounts.change_talentprofile',
                    'accounts.view_brandprofile',
                    'accounts.add_brandprofile',
                    'accounts.change_brandprofile',
                ]
            },
            'Campaign Manager': {
                'description': 'Manage campaigns and applications',
                'permissions': [
                    'campaigns.view_campaign',
                    'campaigns.add_campaign',
                    'campaigns.change_campaign',
                    'campaigns.delete_campaign',
                    'campaigns.view_talentcampaignapplication',
                    'campaigns.change_talentcampaignapplication',
                    'campaigns.view_proposal',
                    'campaigns.change_proposal',
                ]
            },
            'Content Manager': {
                'description': 'Manage content uploads and scripts',
                'permissions': [
                    'campaigns.view_contentupload',
                    'campaigns.change_contentupload',
                    'campaigns.view_script',
                    'campaigns.change_script',
                    'accounts.view_projects',
                    'accounts.change_projects',
                ]
            },
            'Analytics Viewer': {
                'description': 'View analytics and reports',
                'permissions': [
                    'accounts.view_user',
                    'accounts.view_talentprofile',
                    'accounts.view_brandprofile',
                    'campaigns.view_campaign',
                    'campaigns.view_talentcampaignapplication',
                ]
            },
            'Support Admin': {
                'description': 'Handle support and user issues',
                'permissions': [
                    'accounts.view_user',
                    'accounts.change_user',
                    'accounts.view_talentprofile',
                    'accounts.view_brandprofile',
                    'campaigns.view_campaign',
                    'campaigns.view_talentcampaignapplication',
                ]
            }
        }
        
        created_count = 0
        updated_count = 0
        
        for role_name, role_data in default_roles.items():
            role, created = Role.objects.get_or_create(
                name=role_name,
                defaults={
                    'description': role_data['description'],
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f"Created role: {role_name}")
            else:
                updated_count += 1
                role.description = role_data['description']
                role.save()
                self.stdout.write(f"Updated role: {role_name}")
            
            # Clear existing permissions
            role.permissions.clear()
            
            # Add permissions
            if role_data['permissions'] == ['*']:
                # Add all permissions for Super Admin
                all_permissions = Permission.objects.all()
                role.permissions.set(all_permissions)
                self.stdout.write(f"  Added ALL permissions to {role_name}")
            else:
                # Add specific permissions
                permissions_added = 0
                for perm_codename in role_data['permissions']:
                    try:
                        app_label, codename = perm_codename.split('.')
                        permission = Permission.objects.get(
                            codename=codename,
                            content_type__app_label=app_label
                        )
                        role.permissions.add(permission)
                        permissions_added += 1
                    except Permission.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(f"  Permission not found: {perm_codename}")
                        )
                    except ValueError:
                        self.stdout.write(
                            self.style.ERROR(f"  Invalid permission format: {perm_codename}")
                        )
                
                self.stdout.write(f"  Added {permissions_added} permissions to {role_name}")
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\nRoles setup complete! Created: {created_count}, Updated: {updated_count}"
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                "You can now assign these roles to users in the Django admin panel."
            )
        )
