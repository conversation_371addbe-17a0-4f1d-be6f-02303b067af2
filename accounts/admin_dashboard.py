from django.contrib import admin
from django.shortcuts import render
from django.db.models import Count, Q
from django.urls import path
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.core.cache import cache

from accounts.models import User, TalentProfile, BrandProfile, UserSubscription
from accounts.choices import UserTypeChoices
from campaigns.models import Campaign
from talent_trade.utils.choices import CampaignStatusChoices


class DashboardStatsAdminSite(admin.AdminSite):
    """Custom admin site with dashboard statistics"""
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('dashboard-stats/', self.admin_view(self.dashboard_stats_view), name='dashboard_stats'),
        ]
        return custom_urls + urls

    @method_decorator(staff_member_required)
    def dashboard_stats_view(self, request):
        """View to display dashboard statistics"""
        
        # Get user statistics
        total_talents = User.objects.filter(
            user_type=UserTypeChoices.TALENT.value[0],
            is_active=True
        ).count()
        
        total_brands = User.objects.filter(
            user_type=UserTypeChoices.BRAND.value[0],
            is_active=True
        ).count()
        
        # Get paid subscribers statistics
        total_paid_subscribers_talent = UserSubscription.objects.filter(
            user__user_type=UserTypeChoices.TALENT.value[0],
            is_active=True
        ).exclude(subscription_type='free').count()

        total_paid_subscribers_brand = UserSubscription.objects.filter(
            user__user_type=UserTypeChoices.BRAND.value[0],
            is_active=True
        ).exclude(subscription_type='free').count()
        
        # Get campaign statistics
        total_campaigns_active = Campaign.objects.filter(
            status=CampaignStatusChoices.ACTIVE
        ).count()
        
        total_campaigns_draft = Campaign.objects.filter(
            status=CampaignStatusChoices.DRAFT
        ).count()
        
        total_campaigns_archived = Campaign.objects.filter(
            status=CampaignStatusChoices.ARCHIVED
        ).count()
        
        # Get app downloads from cache (updated by management command)
        total_app_downloads = cache.get('total_app_downloads', 0)
        
        context = {
            'title': 'Dashboard Statistics',
            'stats': {
                'total_talents': total_talents,
                'total_brands': total_brands,
                'total_paid_subscribers_talent': total_paid_subscribers_talent,
                'total_paid_subscribers_brand': total_paid_subscribers_brand,
                'total_campaigns_active': total_campaigns_active,
                'total_campaigns_draft': total_campaigns_draft,
                'total_campaigns_archived': total_campaigns_archived,
                'total_app_downloads': total_app_downloads,
            }
        }
        
        return render(request, 'admin/dashboard_stats.html', context)

    def index(self, request, extra_context=None):
        """Override the default admin index to include our stats"""
        extra_context = extra_context or {}
        
        # Add quick stats to the main admin index
        extra_context.update({
            'total_talents': User.objects.filter(
                user_type=UserTypeChoices.TALENT.value[0],
                is_active=True
            ).count(),
            'total_brands': User.objects.filter(
                user_type=UserTypeChoices.BRAND.value[0],
                is_active=True
            ).count(),
            'total_campaigns_active': Campaign.objects.filter(
                status=CampaignStatusChoices.ACTIVE
            ).count(),
        })
        
        return super().index(request, extra_context)


# Function to get dashboard statistics (can be used elsewhere)
def get_dashboard_statistics():
    """Utility function to get all dashboard statistics"""
    return {
        'total_talents': User.objects.filter(
            user_type=UserTypeChoices.TALENT.value[0],
            is_active=True
        ).count(),
        'total_brands': User.objects.filter(
            user_type=UserTypeChoices.BRAND.value[0],
            is_active=True
        ).count(),
        'total_paid_subscribers_talent': UserSubscription.objects.filter(
            user__user_type=UserTypeChoices.TALENT.value[0],
            is_active=True
        ).exclude(subscription_type='free').count(),
        'total_paid_subscribers_brand': UserSubscription.objects.filter(
            user__user_type=UserTypeChoices.BRAND.value[0],
            is_active=True
        ).exclude(subscription_type='free').count(),
        'total_campaigns_active': Campaign.objects.filter(
            status=CampaignStatusChoices.ACTIVE
        ).count(),
        'total_campaigns_draft': Campaign.objects.filter(
            status=CampaignStatusChoices.DRAFT
        ).count(),
        'total_campaigns_archived': Campaign.objects.filter(
            status=CampaignStatusChoices.ARCHIVED
        ).count(),
        'total_app_downloads': cache.get('total_app_downloads', 0),
    }
