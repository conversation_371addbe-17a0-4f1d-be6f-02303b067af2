from django.contrib import admin
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator

from accounts.models import User
from accounts.choices import UserTypeChoices
from campaigns.models import Campaign
from talent_trade.utils.choices import CampaignStatusChoices


@method_decorator(staff_member_required, name='dispatch')
def dashboard_stats_view(request):
    """Simple dashboard statistics view"""
    
    # Get user statistics
    total_talents = User.objects.filter(
        user_type=UserTypeChoices.TALENT.value[0],
        is_active=True
    ).count()
    
    total_brands = User.objects.filter(
        user_type=UserTypeChoices.BRAND.value[0],
        is_active=True
    ).count()
    
    # Paid subscribers - showing 0 for now as requested
    total_paid_subscribers_talent = 0
    total_paid_subscribers_brand = 0
    
    # Get campaign statistics
    total_campaigns_active = Campaign.objects.filter(
        status=CampaignStatusChoices.ACTIVE
    ).count()
    
    total_campaigns_draft = Campaign.objects.filter(
        status=CampaignStatusChoices.DRAFT
    ).count()
    
    total_campaigns_archived = Campaign.objects.filter(
        status=CampaignStatusChoices.ARCHIVED
    ).count()
    
    # App downloads - showing 0 for now as requested
    total_app_downloads = 0
    
    context = {
        'title': 'Dashboard Statistics',
        'stats': {
            'total_talents': total_talents,
            'total_brands': total_brands,
            'total_paid_subscribers_talent': total_paid_subscribers_talent,
            'total_paid_subscribers_brand': total_paid_subscribers_brand,
            'total_campaigns_active': total_campaigns_active,
            'total_campaigns_draft': total_campaigns_draft,
            'total_campaigns_archived': total_campaigns_archived,
            'total_app_downloads': total_app_downloads,
        }
    }
    
    return render(request, 'admin/dashboard_stats.html', context)


def get_dashboard_statistics():
    """Simple function to get dashboard statistics for admin index"""
    return {
        'total_talents': User.objects.filter(
            user_type=UserTypeChoices.TALENT.value[0],
            is_active=True
        ).count(),
        'total_brands': User.objects.filter(
            user_type=UserTypeChoices.BRAND.value[0],
            is_active=True
        ).count(),
        'total_campaigns_active': Campaign.objects.filter(
            status=CampaignStatusChoices.ACTIVE
        ).count(),
    }
