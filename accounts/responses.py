from rest_framework.response import Response
import enum
from rest_framework import pagination, response, status
from rest_framework import serializers
from django.conf import settings
from rest_framework.exceptions import ValidationError

class Responses(object):
    @staticmethod
    def get_first_error_message(data):
        """
        Extract first error message from DRF validation error data
        """
        if isinstance(data, dict):
            # Handle non_field_errors first
            if 'non_field_errors' in data:
                return data['non_field_errors'][0]
            
            # Handle field-specific errors
            for field, errors in data.items():
                if isinstance(errors, list) and errors:
                    return errors[0]
                elif isinstance(errors, str):
                    return errors
                elif isinstance(errors, dict):
                    # Handle nested serializer errors
                    nested_message = Responses.get_first_error_message(errors)
                    if nested_message:
                        return nested_message
            
        elif isinstance(data, list) and data:
            return data[0]
        
        return "Validation error"

    @classmethod
    def handle_validation_error(cls, error_data):
        """
        Common handler for validation errors
        """
        error_message = cls.get_first_error_message(error_data)
        return cls.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message=error_message,
            data=error_data
        )

    @classmethod
    def success_response(cls, status, message, data=None): 
        response = {
            "status": True,
            "message": message,
            "data": data if data is not None else {}
        }
        return Response(data=response, status=status)

    @classmethod
    def error_response(cls, status, message, data=None):
        # If data contains validation errors, extract the message
        if isinstance(data, dict) and any(key in data for key in ['non_field_errors', 'detail']):
            message = cls.get_first_error_message(data)
        
        response = {
            "status": False,
            "message": message,
            "data": data if data is not None else {}
        }
        return Response(data=response, status=status) 
