from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import Group
from django.core.exceptions import ObjectDoesNotExist
import nested_admin
from django import forms
from campaigns.models import TalentCampaignApplication
from .models import (
    BrandUser, PlatformCategory, SocialMediaPlatform, TalentUser, User, SuperAdminUser, BrandProfile, TalentProfile,
    Projects, BankAccountDetails, SocialRateCard, SocialConnects, SocialMediaAnalytics,
    SocialMediaPost, BrandAdmin, Role, UserRole
)
from .admin_dashboard import get_dashboard_statistics
from .choices import UserTypeChoices

# Forward declaration for UserRoleInline (defined later)
class UserRoleInline(admin.TabularInline):
    model = None  # Will be set later
    extra = 0

class SocialRateCardForm(forms.ModelForm):
    class Meta:
        model = SocialRateCard
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        self.user = getattr(self, 'user', None)
        super().__init__(*args, **kwargs)

        # If form was attached to user from inline
        if self.user:
            social_tags = SocialMediaPlatform.objects.filter(
                projects__user=self.user
            ).distinct()

            self.fields['platform_category'].queryset = PlatformCategory.objects.filter(
                platform__in=social_tags
            )

class CampaignApplicationInline(admin.TabularInline):
    model = TalentCampaignApplication
    extra = 0
    fields = ('campaign', 'status')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if self.parent_object:
            qs = qs.filter(talent__user=self.parent_object)
        return qs

    @property
    def parent_object(self):
        return getattr(self, 'parent_instance', None)



class   TalentProfileInline(nested_admin.NestedStackedInline):
    model = TalentProfile
    extra = 0

    inlines = [CampaignApplicationInline]


    
class BrandAdminInline(admin.TabularInline):
    model = BrandAdmin
    extra = 0
    fields = ('name', 'email', 'phone', 'is_primary')


class ProjectsInline(admin.StackedInline):
    model = Projects
    extra = 0
    fields = (
        'title',
        'description',
        'image',
        'video',
        'thumbnail',
        'social_tags',
        'category_tags',
    )
    readonly_fields = ('created_at', 'updated_at')
    filter_horizontal = ('social_tags', 'category_tags')  # ManyToMany fields nicely handled

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        formset._parent_project_instance = obj
        return formset
    
class SocialRateCardInline(admin.TabularInline):
    model = SocialRateCard
    form = SocialRateCardForm
    extra = 1

    def get_formset(self, request, obj=None, **kwargs):
        FormSet = super().get_formset(request, obj, **kwargs)

        class PrefilledFormSet(FormSet):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                for form in self.forms:
                    form.user = obj  # Set user on each form

        return PrefilledFormSet
class BankAccountDetailsInline(admin.StackedInline):
    model = BankAccountDetails
    extra = 0
    fields = ('account_holder_name', 'account_number', 'ifsc_code', 'aadhar_number', 'pan_number')
    readonly_fields = ('created_at', 'updated_at')


class CustomUserAdmin(BaseUserAdmin):
    list_display = ('email', 'mobile_number', 'user_type', 'is_active', 'date_joined')
    ordering = ('-date_joined',)

    fieldsets = (
        ('Personal info', {'fields': ('email', 'username', 'mobile_number', 'country_code')}),
        ('User Type', {'fields': ('user_type', 'is_admin_user')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('Verification', {'fields': ('is_email_verified', 'is_mobile_verified', 'kyc_status')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'mobile_number'),
        }),
    )


class SuperAdminUserAdmin(CustomUserAdmin):
    inlines = [UserRoleInline]

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_superuser=True)


class BrandUserAdmin(CustomUserAdmin):
    list_display = ('email', 'mobile_number', 'brand_name', 'is_active', 'date_joined')
    inlines = [BrandAdminInline,
        ProjectsInline,
        BankAccountDetailsInline,
        ]

    def brand_name(self, obj):
        try:
            return obj.brand_profile.brand_name
        except ObjectDoesNotExist:
            return '-'
    brand_name.short_description = 'Brand Name'

    def get_queryset(self, request):
        return super().get_queryset(request).filter(user_type=UserTypeChoices.BRAND.value[0])

class TalentUserAdmin(CustomUserAdmin):
    list_display = ('email', 'mobile_number', 'talent_name', 'is_active', 'date_joined')
    readonly_fields = ['display_campaign_applications']
    inlines = [
        ProjectsInline,
        BankAccountDetailsInline,
        SocialRateCardInline,
    ]

    fieldsets = CustomUserAdmin.fieldsets + (
        ('Campaign Applications', {
            'fields': ('display_campaign_applications',)
        }),
    )

    def talent_name(self, obj):
        try:
            return obj.talent_profile.name
        except ObjectDoesNotExist:
            return '-'
    talent_name.short_description = 'Talent Name'

    def display_campaign_applications(self, obj):
        try:
            talent_profile = obj.talent_profile  # may raise ObjectDoesNotExist
            applications = talent_profile.applications.all()

            if not applications.exists():
                return "No campaign applications."

            return ", ".join([f"{app.campaign} ({app.get_status_display()})" for app in applications])
        except ObjectDoesNotExist:
            return "No TalentProfile found."
        except Exception as e:
            return f"Error fetching applications: {str(e)}"

    def get_queryset(self, request):
        return super().get_queryset(request).filter(user_type=UserTypeChoices.TALENT.value[0])


# Register Proxy Models
admin.site.register(SuperAdminUser, SuperAdminUserAdmin)
admin.site.register(BrandUser, BrandUserAdmin)
admin.site.register(TalentUser, TalentUserAdmin)

class HiddenUserAdmin(CustomUserAdmin):
    def has_module_permission(self, request):
        return False
admin.site.register(User, HiddenUserAdmin)

admin.site.unregister(Group)


# Update the UserRoleInline definition
UserRoleInline.model = UserRole
UserRoleInline.fields = ('role', 'is_active', 'assigned_by', 'created_at')
UserRoleInline.readonly_fields = ('created_at', 'assigned_by')

def save_userrole_model(self, request, obj, form, change):
    if not change:  # If creating new role assignment
        obj.assigned_by = request.user
    super(UserRoleInline, self).save_model(request, obj, form, change)

UserRoleInline.save_model = save_userrole_model


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'permission_count', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    filter_horizontal = ('permissions',)
    readonly_fields = ('created_at', 'updated_at', 'permission_count')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Permissions', {
            'fields': ('permissions',),
            'description': 'Select Django permissions to assign to this role'
        }),
        ('Meta Information', {
            'fields': ('permission_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def permission_count(self, obj):
        return obj.permission_count
    permission_count.short_description = 'Permissions Count'


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'is_active', 'assigned_by', 'created_at')
    list_filter = ('role', 'is_active', 'created_at')
    search_fields = ('user__email', 'user__username', 'role__name')
    readonly_fields = ('created_at', 'updated_at', 'assigned_by')

    fieldsets = (
        ('Role Assignment', {
            'fields': ('user', 'role', 'is_active')
        }),
        ('Assignment Info', {
            'fields': ('assigned_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new role assignment
            obj.assigned_by = request.user
        super().save_model(request, obj, form, change)


# Override the admin site index to include dashboard statistics
original_index = admin.site.index

def custom_admin_index(request, extra_context=None):
    """Custom admin index that includes dashboard statistics"""
    extra_context = extra_context or {}
    stats = get_dashboard_statistics()
    extra_context.update({
        'total_talents': stats['total_talents'],
        'total_brands': stats['total_brands'],
        'total_campaigns_active': stats['total_campaigns_active'],
    })
    return original_index(request, extra_context)

admin.site.index = custom_admin_index
