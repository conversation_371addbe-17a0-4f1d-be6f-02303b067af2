from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import Group
from django.core.exceptions import ObjectDoesNotExist
import nested_admin
from django import forms
from campaigns.models import TalentCampaignApplication
from .models import (
    BrandUser, PlatformCategory, SocialMediaPlatform, TalentUser, User, SuperAdminUser, BrandProfile, TalentProfile,
    Projects, BankAccountDetails, SocialRateCard, SocialConnects, SocialMediaAnalytics,
    SocialMediaPost, BrandAdmin
)
from .choices import UserTypeChoices

class SocialRateCardForm(forms.ModelForm):
    class Meta:
        model = SocialRateCard
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        self.user = getattr(self, 'user', None)
        super().__init__(*args, **kwargs)

        # If form was attached to user from inline
        if self.user:
            social_tags = SocialMediaPlatform.objects.filter(
                projects__user=self.user
            ).distinct()

            self.fields['platform_category'].queryset = PlatformCategory.objects.filter(
                platform__in=social_tags
            )

class CampaignApplicationInline(admin.TabularInline):
    model = TalentCampaignApplication
    extra = 0
    fields = ('campaign', 'status')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if self.parent_object:
            qs = qs.filter(talent__user=self.parent_object)
        return qs

    @property
    def parent_object(self):
        return getattr(self, 'parent_instance', None)



class   TalentProfileInline(nested_admin.NestedStackedInline):
    model = TalentProfile
    extra = 0

    inlines = [CampaignApplicationInline]


    
class BrandAdminInline(admin.TabularInline):
    model = BrandAdmin
    extra = 0
    fields = ('name', 'email', 'phone', 'is_primary')


class ProjectsInline(nested_admin.NestedStackedInline):   # <--- changed here
    model = Projects
    extra = 0
    fields = (
        'title', 
        'description', 
        'image', 
        'video', 
        'thumbnail',
        'social_tags',
        'category_tags',
    )
    readonly_fields = ('created_at', 'updated_at')
    # filter_horizontal = ('social_tags', 'category_tags')  # ManyToMany fields nicely handled
    # inlines = [SocialRateCardInline]
    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        formset._parent_project_instance = obj
        return formset
    
class SocialRateCardInline(admin.TabularInline):
    model = SocialRateCard
    form = SocialRateCardForm
    extra = 1

    def get_formset(self, request, obj=None, **kwargs):
        FormSet = super().get_formset(request, obj, **kwargs)

        class PrefilledFormSet(FormSet):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                for form in self.forms:
                    form.user = obj  # Set user on each form

        return PrefilledFormSet
class BankAccountDetailsInline(admin.StackedInline):
    model = BankAccountDetails
    extra = 0
    fields = ('account_holder_name', 'account_number', 'ifsc_code', 'aadhar_number', 'pan_number')
    readonly_fields = ('created_at', 'updated_at')


class CustomUserAdmin(BaseUserAdmin):
    list_display = ('email', 'mobile_number', 'user_type', 'is_active', 'date_joined')
    ordering = ('-date_joined',)

    fieldsets = (
        ('Personal info', {'fields': ('email', 'username', 'mobile_number', 'country_code')}),
        ('User Type', {'fields': ('user_type', 'is_admin_user')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('Verification', {'fields': ('is_email_verified', 'is_mobile_verified', 'kyc_status')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'mobile_number'),
        }),
    )


class SuperAdminUserAdmin(CustomUserAdmin):
    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_superuser=True)


class BrandUserAdmin(CustomUserAdmin):
    list_display = ('email', 'mobile_number', 'brand_name', 'is_active', 'date_joined')
    inlines = [BrandAdminInline,
        ProjectsInline,
        BankAccountDetailsInline,
        ]

    def brand_name(self, obj):
        try:
            return obj.brand_profile.brand_name
        except ObjectDoesNotExist:
            return '-'
    brand_name.short_description = 'Brand Name'

    def get_queryset(self, request):
        return super().get_queryset(request).filter(user_type=UserTypeChoices.BRAND.value[0])

class TalentUserAdmin(CustomUserAdmin):
    list_display = ('email', 'mobile_number', 'talent_name', 'is_active', 'date_joined')
    readonly_fields = ['display_campaign_applications'] 
    inlines = [
        ProjectsInline,
        BankAccountDetailsInline,
        SocialRateCardInline,
    ]

    fieldsets = CustomUserAdmin.fieldsets + (
        ('Campaign Applications', {
            'fields': ('display_campaign_applications',)
        }),
    )

    def talent_name(self, obj):
        try:
            return obj.talent_profile.name
        except ObjectDoesNotExist:
            return '-'
    talent_name.short_description = 'Talent Name'

    def display_campaign_applications(self, obj):
        try:
            talent_profile = obj.talent_profile  # may raise ObjectDoesNotExist
            applications = talent_profile.applications.all()

            if not applications.exists():
                return "No campaign applications."

            return ", ".join([f"{app.campaign} ({app.get_status_display()})" for app in applications])
        except ObjectDoesNotExist:
            return "No TalentProfile found."
        except Exception as e:
            return f"Error fetching applications: {str(e)}"

    def get_queryset(self, request):
        return super().get_queryset(request).filter(user_type=UserTypeChoices.TALENT.value[0])


# Register Proxy Models
admin.site.register(SuperAdminUser, SuperAdminUserAdmin)
admin.site.register(BrandUser, BrandUserAdmin)
admin.site.register(TalentUser, TalentUserAdmin)

class HiddenUserAdmin(CustomUserAdmin):
    def has_module_permission(self, request):
        return False
admin.site.register(User, HiddenUserAdmin)

admin.site.unregister(Group)
