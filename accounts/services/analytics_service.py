import requests
from django.conf import settings
from django.utils import timezone
from accounts.models import SocialMediaAnalytics, SocialMediaPost, SocialConnects
from typing import Dict, Any
from datetime import datetime

class AnalyticsService:
    """Service to fetch and process social media analytics"""

    @classmethod
    def fetch_analytics(cls, social_connect: SocialConnects) -> Dict[str, Any]:
        """Fetch analytics based on platform type"""
        platform_name = social_connect.platform.name.lower()
        
        if platform_name == 'instagram':
            return cls._fetch_instagram_analytics(social_connect)
        elif platform_name == 'youtube':
            return cls._fetch_youtube_analytics(social_connect)
        elif platform_name == 'facebook':
            return cls._fetch_facebook_analytics(social_connect)
        elif platform_name == 'twitter':
            return cls._fetch_twitter_analytics(social_connect)
        elif platform_name == 'linkedin':
            return cls._fetch_linkedin_analytics(social_connect)
        else:
            raise ValueError(f"Unsupported platform: {platform_name}")

    @classmethod
    def _fetch_instagram_analytics(cls, social_connect: SocialConnects) -> Dict[str, Any]:
        """Fetch Instagram analytics using Graph API"""
        try:
            # Get credentials
            credentials = social_connect.oauth_credentials.first()
            if not credentials:
                raise ValueError("No OAuth credentials found")

            # Fetch basic profile data
            profile_response = requests.get(
                f'https://graph.instagram.com/{credentials.platform_user_id}',
                params={
                    'access_token': credentials.access_token,
                    'fields': 'id,username,media_count,followers_count,follows_count'
                }
            )
            profile_response.raise_for_status()
            profile_data = profile_response.json()

            # Fetch recent posts
            posts_response = requests.get(
                f'https://graph.instagram.com/{credentials.platform_user_id}/media',
                params={
                    'access_token': credentials.access_token,
                    'fields': 'id,caption,media_type,media_url,permalink,timestamp,like_count,comments_count'
                }
            )
            posts_response.raise_for_status()
            posts_data = posts_response.json()

            # Process analytics
            analytics_data = {
                'followers_count': profile_data.get('followers_count', 0),
                'following_count': profile_data.get('follows_count', 0),
                'posts_count': profile_data.get('media_count', 0),
                'posts': []
            }

            # Process posts
            total_reach = 0
            total_engagement = 0
            
            for post in posts_data.get('data', []):
                engagement = post.get('like_count', 0) + post.get('comments_count', 0)
                reach = cls._calculate_instagram_reach(post, analytics_data['followers_count'])
                
                post_data = {
                    'post_id': post['id'],
                    'post_url': post['permalink'],
                    'post_type': post['media_type'].lower(),
                    'reach': reach,
                    'engagement': engagement,
                    'likes': post.get('like_count', 0),
                    'comments': post.get('comments_count', 0),
                    'shares': 0,  # Instagram API doesn't provide shares count
                    'posted_at': datetime.strptime(post['timestamp'], '%Y-%m-%dT%H:%M:%S%z')
                }
                
                analytics_data['posts'].append(post_data)
                total_reach += reach
                total_engagement += engagement

            # Calculate averages
            post_count = len(analytics_data['posts'])
            if post_count > 0:
                analytics_data['average_reach'] = total_reach // post_count
                analytics_data['average_engagement'] = total_engagement / post_count
            else:
                analytics_data['average_reach'] = 0
                analytics_data['average_engagement'] = 0

            # Save to database
            analytics = SocialMediaAnalytics.objects.create(
                social_connect=social_connect,
                followers_count=analytics_data['followers_count'],
                following_count=analytics_data['following_count'],
                posts_count=analytics_data['posts_count'],
                average_reach=analytics_data['average_reach'],
                average_engagement=analytics_data['average_engagement']
            )

            # Save posts
            for post_data in analytics_data['posts']:
                SocialMediaPost.objects.create(
                    analytics=analytics,
                    **post_data
                )

            return analytics_data

        except Exception as e:
            raise Exception(f"Failed to fetch Instagram analytics: {str(e)}")

    @classmethod
    def _fetch_youtube_analytics(cls, social_connect: SocialConnects) -> Dict[str, Any]:
        """Fetch YouTube analytics using Data API"""
        try:
            # Check for required channel ID
            if not social_connect.platform_user_id:
                raise ValueError("Missing channel ID")

            if not settings.YOUTUBE_API_KEY:
                raise ValueError("YouTube API key is not configured")

            # First try to get channel by username if it's not a channel ID
            if not social_connect.platform_user_id.startswith('UC'):
                username_response = requests.get(
                    'https://www.googleapis.com/youtube/v3/channels',
                    params={
                        'part': 'id',
                        'forUsername': social_connect.platform_user_id,
                        'key': settings.YOUTUBE_API_KEY
                    }
                )
                username_response.raise_for_status()
                username_data = username_response.json()
                
                if 'items' in username_data and username_data['items']:
                    channel_id = username_data['items'][0]['id']
                    # Update the stored channel ID
                    social_connect.platform_user_id = channel_id
                    social_connect.save()
                else:
                    # Try search as a fallback
                    search_response = requests.get(
                        'https://www.googleapis.com/youtube/v3/search',
                        params={
                            'part': 'snippet',
                            'q': social_connect.platform_user_id,
                            'type': 'channel',
                            'key': settings.YOUTUBE_API_KEY
                        }
                    )
                    search_response.raise_for_status()
                    search_data = search_response.json()
                    
                    if 'items' in search_data and search_data['items']:
                        channel_id = search_data['items'][0]['snippet']['channelId']
                        # Update the stored channel ID
                        social_connect.platform_user_id = channel_id
                        social_connect.save()
                    else:
                        raise ValueError(f"Could not find channel with username: {social_connect.platform_user_id}")

            # Fetch channel statistics
            channel_response = requests.get(
                'https://www.googleapis.com/youtube/v3/channels',
                params={
                    'part': 'statistics',
                    'id': social_connect.platform_user_id,
                    'key': settings.YOUTUBE_API_KEY
                }
            )
            channel_response.raise_for_status()
            channel_data = channel_response.json()
            
            if 'items' not in channel_data or not channel_data['items']:
                raise ValueError(f"No channel found with ID: {social_connect.platform_user_id}")
            
            channel_stats = channel_data['items'][0]['statistics']

            # Fetch recent videos
            videos_response = requests.get(
                'https://www.googleapis.com/youtube/v3/search',
                params={
                    'part': 'snippet',
                    'channelId': social_connect.platform_user_id,
                    'maxResults': 50,
                    'order': 'date',
                    'type': 'video',
                    'key': settings.YOUTUBE_API_KEY
                }
            )
            videos_response.raise_for_status()
            videos_data = videos_response.json()

            if 'items' not in videos_data:
                raise ValueError("Failed to fetch videos data")

            # Process analytics
            analytics_data = {
                'followers_count': int(channel_stats.get('subscriberCount', 0)),
                'following_count': 0,  # YouTube doesn't have following
                'posts_count': int(channel_stats.get('videoCount', 0)),
                'posts': []
            }

            # Process videos
            total_reach = 0
            total_engagement = 0

            for video in videos_data['items']:
                try:
                    video_id = video['id']['videoId']
                    
                    # Fetch video statistics
                    video_stats_response = requests.get(
                        'https://www.googleapis.com/youtube/v3/videos',
                        params={
                            'part': 'statistics',
                            'id': video_id,
                            'key': settings.YOUTUBE_API_KEY
                        }
                    )
                    video_stats_response.raise_for_status()
                    video_stats_data = video_stats_response.json()
                    
                    if 'items' not in video_stats_data or not video_stats_data['items']:
                        continue
                        
                    video_stats = video_stats_data['items'][0]['statistics']

                    reach = int(video_stats.get('viewCount', 0))
                    likes = int(video_stats.get('likeCount', 0))
                    comments = int(video_stats.get('commentCount', 0))
                    engagement = likes + comments

                    post_data = {
                        'post_id': video_id,
                        'post_url': f'https://www.youtube.com/watch?v={video_id}',
                        'post_type': 'video',
                        'reach': reach,
                        'engagement': engagement,
                        'likes': likes,
                        'comments': comments,
                        'shares': 0,  # YouTube API doesn't provide shares count
                        'posted_at': datetime.strptime(video['snippet']['publishedAt'], '%Y-%m-%dT%H:%M:%SZ')
                    }

                    analytics_data['posts'].append(post_data)
                    total_reach += reach
                    total_engagement += engagement
                except Exception as e:
                    print(f"Error processing video {video_id}: {str(e)}")
                    continue

            # Calculate averages
            post_count = len(analytics_data['posts'])
            if post_count > 0:
                analytics_data['average_reach'] = total_reach // post_count
                analytics_data['average_engagement'] = total_engagement / post_count
            else:
                analytics_data['average_reach'] = 0
                analytics_data['average_engagement'] = 0

            # Save to database
            analytics = SocialMediaAnalytics.objects.create(
                social_connect=social_connect,
                followers_count=analytics_data['followers_count'],
                following_count=analytics_data['following_count'],
                posts_count=analytics_data['posts_count'],
                average_reach=analytics_data['average_reach'],
                average_engagement=analytics_data['average_engagement']
            )

            # Save posts
            for post_data in analytics_data['posts']:
                SocialMediaPost.objects.create(
                    analytics=analytics,
                    **post_data
                )

            return analytics_data

        except requests.exceptions.RequestException as e:
            raise Exception(f"YouTube API request failed: {str(e)}")
        except ValueError as e:
            raise Exception(str(e))
        except Exception as e:
            raise Exception(f"Failed to fetch YouTube analytics: {str(e)}")

    @staticmethod
    def _calculate_instagram_reach(post: Dict[str, Any], followers_count: int) -> int:
        """Calculate estimated reach for Instagram post"""
        # This is a simplified calculation - you might want to use more sophisticated logic
        engagement = post.get('like_count', 0) + post.get('comments_count', 0)
        engagement_rate = engagement / followers_count if followers_count > 0 else 0
        
        # Estimate reach based on engagement rate
        if engagement_rate > 0.1:  # High engagement
            reach = int(followers_count * 0.5)  # 50% of followers
        elif engagement_rate > 0.05:  # Medium engagement
            reach = int(followers_count * 0.3)  # 30% of followers
        else:  # Low engagement
            reach = int(followers_count * 0.1)  # 10% of followers
            
        return reach

    @classmethod
    def _fetch_facebook_analytics(cls, social_connect: SocialConnects) -> Dict[str, Any]:
        """Fetch Facebook analytics"""
        # Implement Facebook analytics fetching
        raise NotImplementedError("Facebook analytics not implemented yet")

    @classmethod
    def _fetch_twitter_analytics(cls, social_connect: SocialConnects) -> Dict[str, Any]:
        """Fetch Twitter analytics"""
        # Implement Twitter analytics fetching
        raise NotImplementedError("Twitter analytics not implemented yet")

    @classmethod
    def _fetch_linkedin_analytics(cls, social_connect: SocialConnects) -> Dict[str, Any]:
        """Fetch LinkedIn analytics"""
        # Implement LinkedIn analytics fetching
        raise NotImplementedError("LinkedIn analytics not implemented yet") 