
import uuid
import os
import boto3
from django.conf import settings
from rest_framework.validators import ValidationError
from talent_trade.utils.choices import S3FilePathChoices


def get_env_list(var_name, default=""):
    return os.getenv(var_name, default).split(",")

ALLOWED_EXTENSIONS = {
    "audio": get_env_list("ALLOWED_AUDIO_EXTENSIONS"),
    "video": get_env_list("ALLOWED_VIDEO_EXTENSIONS"),
    "file": get_env_list("ALLOWED_FILE_EXTENSIONS"),
    "image": get_env_list("ALLOWED_IMAGE_EXTENSIONS"),
}
class S3Client:
    """
    A class to initialize and provide an S3 client instance.
    """

    def __init__(
            self,
            service_name,
            region_name=None, 
            aws_access_key_id=None, 
            aws_secret_access_key=None
            ):
       
        self.service_name = service_name
        self.region_name = region_name or settings.AWS_S3_REGION_NAME
        self.aws_access_key_id = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
        self.aws_secret_access_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

    def get_client(self):
        """
        Return the AWS service client instance.
        """
        return boto3.client(
            service_name=self.service_name,
            region_name=self.region_name,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

class PresignedUrlHelper:
    def __init__(self, request_data):
        self.request_data = request_data

    def validate_request_data(self):
        for parent_key, files in self.request_data.items():
            if parent_key not in S3FilePathChoices.values:
                raise ValidationError(f"{parent_key} is invalid choice")

            # Apply limit of max 5 files for project images/videos
            if parent_key in [S3FilePathChoices.PROJECT_IMAGE, S3FilePathChoices.PROJECT_VIDEO] and len(files) > 5:
                raise ValidationError(f"Only up to 5 files allowed for {parent_key}")

            for file in files:
                self.validate_file(file, parent_key)


    def validate_file(self, file, parent_key):
        required_keys = {"file_type", "file_name", "extension"}
        if not required_keys.issubset(file.keys()):
            raise ValidationError(f"Missing keys in file under '{parent_key}'. Required: {required_keys}")

        file_type = file["file_type"]
        extension = file["extension"]

        allowed_extensions = ALLOWED_EXTENSIONS.get(file_type)
        if extension not in allowed_extensions:
            formatted_extensions = ", ".join(allowed_extensions)
            file_type_messages = {
                "image": f"Invalid image format. Please upload a {formatted_extensions} file.",
                "video": f"Invalid video format. Please upload a {formatted_extensions} file.",
                "audio": f"Invalid audio format. Please upload a {formatted_extensions} file.",
                "file": f"Invalid file format. Please upload a {formatted_extensions} file.",
            }
            raise ValidationError(file_type_messages.get(file_type, "Invalid file format."))

        # if not allowed_extensions:
        #     raise ValidationError(f"Invalid file_type '{file_type}' in '{parent_key}'.")
        # if extension not in allowed_extensions:
        #     raise ValidationError(
        #         f"Invalid extension '{extension}' uploaded for {parent_key}. "
        #         f"Supported formats: {', '.join(allowed_extensions)}"
        #     )

        # If video: validate thumbnail, and ensure only one thumbnail exists
        if parent_key == S3FilePathChoices.PROJECT_VIDEO and "thumbnail" in file:
            thumbnail = file["thumbnail"]

            if not isinstance(thumbnail, dict):
                raise ValidationError("Thumbnail must be a single object, not a list or multiple keys.")

            required_thumb_keys = {"file_type", "file_name", "extension"}
            if not required_thumb_keys.issubset(thumbnail.keys()):
                raise ValidationError("Missing keys in thumbnail file.")

            thumb_type = thumbnail["file_type"]
            thumb_ext = thumbnail["extension"]
            allowed_thumb_ext = ALLOWED_EXTENSIONS.get(thumb_type)
            if not allowed_thumb_ext or thumb_ext not in allowed_thumb_ext:
                formatted_thumb_ext = ", ".join(allowed_thumb_ext)
                raise ValidationError(
                    f"Invalid thumbnail format. Please upload a {formatted_thumb_ext} file."
                )


            # if not allowed_thumb_ext or thumb_ext not in allowed_thumb_ext:
            #     raise ValidationError(
            #         f"Invalid thumbnail extension '{thumb_ext}'. Allowed: {', '.join(allowed_thumb_ext)}"
            #     )


    def generate_presigned_urls(self, user_id):
        s3_client = S3Client(service_name="s3").get_client()
        bucket_name = settings.AWS_S3_BUCKET_NAME
        presigned_urls = {}

        for parent_key, files in self.request_data.items():
            s3_folder = dict(S3FilePathChoices.choices)[parent_key]
            presigned_urls[parent_key] = []

            for file in files:
                uuid_file_name = str(uuid.uuid4())
                extension = file["extension"]
                file_type = file["file_type"]

                # Generate file path for the main file
                if parent_key in (S3FilePathChoices.PROJECT_IMAGE, S3FilePathChoices.PROJECT_VIDEO):
                    s3_file_path = f"{s3_folder}{file_type}/{user_id}/{uuid_file_name}.{extension}"
                else:
                    s3_file_path = f"{s3_folder}{user_id}/{uuid_file_name}.{extension}"

                url_data = {
                    "url": s3_client.generate_presigned_url(
                        "put_object",
                        Params={"Bucket": bucket_name, "Key": s3_file_path},
                        ExpiresIn=3600,
                    ),
                    "path": s3_file_path,
                }

                # If video with thumbnail
                if parent_key == S3FilePathChoices.PROJECT_VIDEO and "thumbnail" in file:
                    thumb = file["thumbnail"]
                    thumb_ext = thumb["extension"]
                    thumb_path = f"{dict(S3FilePathChoices.choices)[S3FilePathChoices.PROJECT_VIDEO_THUMBNAIL]}{user_id}/{uuid_file_name}.{thumb_ext}"
                    thumb_url = s3_client.generate_presigned_url(
                        "put_object",
                        Params={"Bucket": bucket_name, "Key": thumb_path},
                        ExpiresIn=3600,
                    )
                    url_data["thumbnail"] = {
                        "url": thumb_url,
                        "path": thumb_path
                    }

                presigned_urls[parent_key].append(url_data)

        return presigned_urls

    @staticmethod
    def delete_file(path):
        try:
            request_data = {"Bucket": settings.AWS_S3_BUCKET_NAME, "Key": str(path)}
            s3_client = S3Client(service_name="s3").get_client()
            s3_client.delete_object(**request_data)
        except Exception:
            pass
