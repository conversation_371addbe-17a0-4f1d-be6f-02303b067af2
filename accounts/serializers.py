from rest_framework import serializers

from talent_trade.utils.custom_serilaizer import OptionC<PERSON>iceField
from .models import *
from .choices import UserTypeChoices
from django.utils import timezone
from django.conf import settings
from urllib.parse import quote
from .models import SocialMediaPlatform, SocialMediaCategoryRate

from django.core.validators import URLValida<PERSON>, RegexValidator
from datetime import date
import re

class OTPRequestSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)
    mobile_number = serializers.CharField(required=False, max_length=15)
    is_signup = serializers.BooleanField(default=False)
    user_type = serializers.CharField(required=False)

    def validate(self, data):
        email = data.get('email')
        mobile_number = data.get('mobile_number')
        user_type = data.get('user_type')
        
        if not email and not mobile_number:
            raise serializers.ValidationError("Either email or mobile number must be provided")
        
        if email:
            data['email'] = email.lower()
        
        # Check if mobile number is already registered with a different user type
        if mobile_number and user_type:
            existing_user = User.objects.filter(mobile_number=mobile_number).first()
            if existing_user and existing_user.user_type != user_type:
                user_type_display = dict(UserTypeChoices.get_choices()).get(existing_user.user_type, existing_user.user_type)
                requested_user_type_display = dict(UserTypeChoices.get_choices()).get(user_type, user_type)
                raise serializers.ValidationError(
                    f"This mobile number is already registered as {user_type_display}. "
                    f"You cannot use it as {requested_user_type_display}."
                )
        
        # Check if email is already registered with a different user type
        if email and user_type:
            existing_user = User.objects.filter(email=email).first()
            if existing_user and existing_user.user_type != user_type:
                user_type_display = dict(UserTypeChoices.get_choices()).get(existing_user.user_type, existing_user.user_type)
                requested_user_type_display = dict(UserTypeChoices.get_choices()).get(user_type, user_type)
                raise serializers.ValidationError(
                    f"This email is already registered as {user_type_display}. "
                    f"You cannot use it as {requested_user_type_display}."
                )
        
        return data

class OTPVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)
    mobile_number = serializers.CharField(required=False, max_length=15)
    otp = serializers.CharField(required=True, max_length=6)
    
    def validate(self, data):
        email = data.get('email')
        phone = data.get('mobile_number')
        
        if not email and not phone:
            raise serializers.ValidationError("Either email or mobile number must be provided")
        
        if email:
            data['email'] = email.lower()
        
        return data


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'email', 'mobile_number', 'user_type', 'username', 'is_email_verified', 'is_mobile_verified') 

class SignupSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(required=True)
    mobile_number = serializers.CharField(required=True)
    user_type = serializers.CharField(required=True)
    username = serializers.CharField(required=False)
    country_code = serializers.CharField(required=False)

    class Meta:
        model = User
        fields = ('email', 'mobile_number', 'user_type', 'username', 'country_code')

    def validate(self, data):
        email = data.get('email')
        mobile_number = data.get('mobile_number')

        # Convert email to lowercase
        if email:
            email = email.lower()
            data['email'] = email

        # Check if email is already registered
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError("Email is already registered")

        # Check if mobile is already registered
        if User.objects.filter(mobile_number=mobile_number).exists():
            raise serializers.ValidationError("Mobile number is already registered")

        # Check if email is verified
        email_verified = OTP.objects.filter(
            email=email,
            is_verified=True,
            created_at__gt=timezone.now() - timezone.timedelta(minutes=30)
        ).exists()

        if not email_verified:
            raise serializers.ValidationError("Email is not verified. Please verify your email first.")

        # Check if mobile is verified
        mobile_verified = OTP.objects.filter(
            phone=mobile_number,
            is_verified=True,
            created_at__gt=timezone.now() - timezone.timedelta(minutes=30)
        ).exists()

        if not mobile_verified:
            raise serializers.ValidationError("Mobile number is not verified. Please verify your mobile number first.")

        return data

    def create(self, validated_data):
        user = User(**validated_data)
        user.is_email_verified = True
        user.is_mobile_verified = True
        user.save()
        return user

class LanguageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Language
        fields = ['id', 'name','value','is_active']

class ExpertiseAreaSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertiseArea
        fields = ['id', 'name', 'icon', 'is_active','type']



class SocialMediaCategoryRateSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='platform_category.name')

    class Meta:
        model = SocialMediaCategoryRate
        fields = ['id', 'name', 'min_price', 'max_price']


class SocialMediaPlatformSerializer(serializers.ModelSerializer):
    social_media_categories = serializers.SerializerMethodField()
    icon = serializers.SerializerMethodField()

    
    class Meta:
        model = SocialMediaPlatform
        fields = ['id', 'name', 'icon', 'social_media_categories']

    def get_social_media_categories(self, obj):
        rates = SocialMediaCategoryRate.objects.filter(platform_category__platform=obj)
        return SocialMediaCategoryRateSerializer(rates, many=True).data
    
    def get_icon(self, obj):
        """Return the CloudFront URL for the file with proper encoding."""
        if obj.icon and hasattr(obj.icon, "name"):
            encoded_path = quote(
                obj.icon.name
            )  # Encode special characters like '+'
            return f"{settings.CLOUDFRONT_DOMAIN}/{encoded_path}"
        return None

class TalentProfileSerializer(serializers.ModelSerializer):
    """Serializer for basic talent profile information (Step 1)"""
    profile_pic = serializers.CharField(max_length=255, required=False, allow_blank=True)
    
    class Meta:
        model = TalentProfile
        fields = ['id', 'profile_pic', 'name', 'gender', 'date_of_birth', 'bio', 'state', 'city', 'is_gst', 'gst_number']

    def validate_date_of_birth(self, value):
        if value and value >= date.today():
            raise serializers.ValidationError("Date of birth must be in the past")
        return value

    def validate_name(self, value):
        if value and len(value.strip()) < 3:
            raise serializers.ValidationError("Name must be at least 3 characters long")
        return value.strip()
        
    def validate_bio(self, value):
        if not value:
            return value
            
        # Check for contact information in bio
        if re.search(r'(\d{10})|(\d{3}[-\.\s]\d{3}[-\.\s]\d{4})', value):  # Phone number pattern
            raise serializers.ValidationError("Contact information is not allowed in bio")
            
        if re.search(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', value):  # Email pattern
            raise serializers.ValidationError("Contact information is not allowed in bio")
            
        # Basic profanity check (would need a more comprehensive solution in production)
        profanity_words = ['profanity1', 'profanity2']  # Replace with actual profanity list
        for word in profanity_words:
            if word in value.lower():
                raise serializers.ValidationError("Bio contains inappropriate language")
                
        return value.strip()

    def validate(self, data):
        is_gst = data.get('is_gst', False)
        gst_number = data.get('gst_number')
        
        if is_gst and not gst_number:
            raise serializers.ValidationError({"gst_number": "GST number is required when GST is enabled"})
        elif not is_gst:
            data['gst_number'] = None
            
        return data

class TalentProfileStepSerializer(serializers.ModelSerializer):
    """Serializer for expertise areas (Step 2)"""
    expertise_areas = serializers.PrimaryKeyRelatedField(many=True, queryset=ExpertiseArea.objects.filter(is_active=True))

    class Meta:
        model = TalentProfile
        fields = ['expertise_areas']

    def validate(self, attrs):
        expertise_areas = attrs.get('expertise_areas')
        if expertise_areas:
            if len(expertise_areas) < 1:
                raise serializers.ValidationError("You must select at least one expertise area")
        return attrs

class ProjectSerializer(serializers.ModelSerializer):
    """Serializer for projects (Step 3)"""
    social_tags = serializers.PrimaryKeyRelatedField(many=True, queryset=SocialMediaPlatform.objects.all(), required=False)
    category_tags = serializers.PrimaryKeyRelatedField(many=True, queryset=ExpertiseArea.objects.filter(is_active=True), required=False)
    image = serializers.CharField(required=False)
    video = serializers.CharField(required=False)
    thumbnail = serializers.CharField(required=False)

    class Meta:
        model = Projects
        fields = ['id', 'title', 'description', 'social_tags', 'category_tags','image','video','thumbnail']

    def validate_title(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("Project title cannot be empty")
        return value.strip()

    def validate_description(self, value):
        if value and len(value.strip()) > 2000:
            raise serializers.ValidationError("Description cannot exceed 2000 characters")
        return value.strip() if value else value

    def validate_social_tags(self, value):
        if value and len(value) > 5:
            raise serializers.ValidationError("You can select up to 5 social tags")
        return value

    def validate(self, data):
        # Check if user already has maximum number of projects
        request = self.context.get('request')
        image = data.get("image")
        video = data.get("video")
        thumbnail = data.get("thumbnail")

        if image and video:
            raise serializers.ValidationError("You can either upload an image or a video, not both.")

        if not image and not video:
            raise serializers.ValidationError("Either image or video must be provided.")

        if video and not thumbnail:
            raise serializers.ValidationError("Thumbnail is required when uploading a video.")

        if image and thumbnail:
            raise serializers.ValidationError("Thumbnail should only be provided when uploading a video.")
        return data

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)

class SocialConnectSerializer(serializers.ModelSerializer):
    """Serializer for social media connections (Step 4)"""
    platform = serializers.PrimaryKeyRelatedField(queryset=SocialMediaPlatform.objects.all())

    class Meta:
        model = SocialConnects
        fields = ['id', 'platform', 'url', 'connected']

    def validate_url(self, value):
        url_validator = URLValidator()
        try:
            url_validator(value)
        except:
            raise serializers.ValidationError("Please enter a valid URL")
        return value

    def validate_platform(self, value):      
        # Check if platform already exists for this user
        existing_connect = SocialConnects.objects.filter(
            user=self.context['request'].user,
            platform=value
        ).first()

        if existing_connect:
            # If there's an existing record, update it instead of creating new
            self.instance = existing_connect
            return value

        # For new connections, check if there's already a connected one
        if SocialConnects.objects.filter(
            user=self.context['request'].user,
            platform=value,
            connected=True
        ).exists():
            raise serializers.ValidationError("This platform is already connected")
        
        return value

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Update the existing instance with new data
        instance.url = validated_data.get('url', instance.url)
        instance.connected = validated_data.get('connected', instance.connected)
        instance.save()
        return instance

class TalentProfileLanguagesSerializer(serializers.ModelSerializer):
    """Serializer for languages (Step 5)"""
    languages = serializers.PrimaryKeyRelatedField(many=True, queryset=Language.objects.filter(is_active=True))

    class Meta:
        model = TalentProfile
        fields = ['languages']

    def validate(self, attrs):
        languages = attrs.get('languages')
        if languages:
            if len(languages) < 1:
                raise serializers.ValidationError("You must select at least one language")
        return attrs

class SocialRateCardSerializer(serializers.ModelSerializer):
    """Serializer for social media rate cards (Step 6)"""
    class Meta:
        model = SocialRateCard
        fields = ['id', 'platform_category', 'min_price', 'max_price']

    def validate(self, data):
        min_price = data.get('min_price')
        max_price = data.get('max_price')
        
        if min_price and max_price:
            if min_price < 0:
                raise serializers.ValidationError({"min_price": "Minimum price cannot be negative"})
            if max_price < 0:
                raise serializers.ValidationError({"max_price": "Maximum price cannot be negative"})
            if min_price > max_price:
                raise serializers.ValidationError({"max_price": "Maximum price must be greater than minimum price"})
        
        return data

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
    
class BankAccountDetailsSerializer(serializers.ModelSerializer):
    """Serializer for bank account details (Step 7)"""

    class Meta:
        model = BankAccountDetails
        fields = ['id', 'account_number', 'ifsc_code', 'account_holder_name', 'aadhar_number', 'pan_number']

    def validate_account_number(self, value):
        if value and not value.strip().isdigit():
            raise serializers.ValidationError("Account number must contain only digits")
        return value.strip()

    def validate_ifsc_code(self, value):
        if value:
            ifsc_pattern = r'^[A-Z]{4}0[A-Z0-9]{6}$'
            if not re.match(ifsc_pattern, value.strip()):
                raise serializers.ValidationError("Please enter a valid IFSC code")
        return value.strip()

    def validate_aadhar_number(self, value):
        if value and (not value.strip().isdigit() or len(value.strip()) != 12):
            raise serializers.ValidationError("Please enter a valid 12-digit Aadhar number")
        return value.strip()

    def validate_pan_number(self, value):
        if value:
            pan_pattern = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
            if not re.match(pan_pattern, value.strip()):
                raise serializers.ValidationError("Please enter a valid PAN number")
        return value.strip()

    def validate_account_holder_name(self, value):
        if value and len(value.strip()) < 3:
            raise serializers.ValidationError("Account holder name must be at least 3 characters long")
        return value.strip()
    
    def mask_middle(self, text, unmasked_end=4):
        """Mask everything except the last `unmasked_end` characters."""
        if not text or len(text) <= unmasked_end:
            return '*' * len(text)
        return '*' * (len(text) - unmasked_end) + text[-unmasked_end:]

    def mask_pan(self, pan):
        """Mask PAN leaving only the last 4 characters."""
        return self.mask_middle(pan, unmasked_end=4) if pan else ""


    def mask_fields(self, validated_data):
        if 'account_number' in validated_data:
            validated_data['account_number'] = self.mask_middle(validated_data['account_number'])
        if 'aadhar_number' in validated_data:
            validated_data['aadhar_number'] = self.mask_middle(validated_data['aadhar_number'])
        if 'pan_number' in validated_data:
            validated_data['pan_number'] = self.mask_pan(validated_data['pan_number'])
        return validated_data

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        masked_data = self.mask_fields(validated_data)
        masked_data['user'] = validated_data['user']  
        return super().create(masked_data)

    def update(self, instance, validated_data):
        masked_data = self.mask_fields(validated_data)
        return super().update(instance, masked_data)



class TalentProfileRetrieveSerializer(serializers.ModelSerializer):
    """Serializer for retrieving complete talent profile with all steps"""
    step_1 = serializers.SerializerMethodField()
    step_2 = serializers.SerializerMethodField()
    step_3 = serializers.SerializerMethodField()
    step_4 = serializers.SerializerMethodField()
    step_5 = serializers.SerializerMethodField()
    step_6 = serializers.SerializerMethodField()
    step_7 = serializers.SerializerMethodField()
    profile_completion = serializers.SerializerMethodField()
    
    class Meta:
        model = TalentProfile
        fields = ['id', 'step_1', 'step_2', 'step_3', 'step_4', 'step_5', 'step_6', 'step_7', 'profile_completion']

    def get_step_1(self, obj):
        """Basic profile information"""
        profile_pic_url = obj.profile_pic.url if obj.profile_pic else None
        # Remove leading slash if it exists
        if profile_pic_url and profile_pic_url.startswith('/'):
            profile_pic_url = profile_pic_url[1:]
            
        return {
            'profile_pic': profile_pic_url,
            'name': obj.name,
            'gender': obj.gender,
            'date_of_birth': obj.date_of_birth,
            'bio': obj.bio,
            'state': obj.state,
            'city': obj.city,
            'is_gst': obj.is_gst,
            'gst_number': obj.gst_number,
            'kyc_status': obj.user.get_kyc_status_display()
        }

    def get_step_2(self, obj):
        """Expertise areas"""
        expertise_areas = obj.expertise_areas.filter(is_active=True)
        return {
            'expertise_areas': ExpertiseAreaSerializer(expertise_areas, many=True).data
        }

    def get_step_3(self, obj):
        """Projects"""
        projects = Projects.objects.filter(user=obj.user).order_by('-created_at')
        return {
            'projects': ProjectSerializer(projects, many=True).data
        }

    def get_step_4(self, obj):
        """Social connects"""
        social_connects = SocialConnects.objects.filter(user=obj.user).select_related('platform')
        return {
            'social_connects': SocialConnectSerializer(social_connects, many=True).data
        }

    def get_step_5(self, obj):
        """Languages"""
        languages = obj.languages.filter(is_active=True)
        return {
            'languages': LanguageSerializer(languages, many=True).data
        }

    def get_step_6(self, obj):
        """Rate cards"""
        rate_cards = SocialRateCard.objects.filter(user=obj.user).select_related('platform_category')
        return {
            'rate_cards': SocialRateCardSerializer(rate_cards, many=True).data
        }

    def get_step_7(self, obj):
        """Bank account details"""
        bank_accounts = BankAccountDetails.objects.filter(user=obj.user).first()
        return {
            'bank_accounts': BankAccountDetailsSerializer(bank_accounts).data if bank_accounts else None
        }
    
    def get_profile_completion(self, obj):
        """Calculate profile completion percentage based on new requirements"""
        completion_percentage = 0
        
        # MANDATORY BASE (50%)
        mandatory_score = 0
        mandatory_total = 50
        
        # Phone + Email verification (10%)
        if obj.user.is_mobile_verified and obj.user.is_email_verified:
            mandatory_score += 10
        
        # Profile photo (5%)
        if obj.profile_pic:
            mandatory_score += 5
        
        # Name (5%)
        if obj.name and len(obj.name.strip()) >= 3:
            mandatory_score += 5
        
        # DOB (5%)
        if obj.date_of_birth:
            mandatory_score += 5
        
        # Bio (5%)
        if obj.bio and len(obj.bio.strip()) > 0:
            mandatory_score += 5
        
        # Areas of Expertise (10%)
        if obj.expertise_areas.filter(is_active=True).exists():
            mandatory_score += 10
        
        # Preferred Languages (10%)
        if obj.languages.filter(is_active=True).exists():
            mandatory_score += 10
        
        # INTERMEDIATE ENHANCED (50%)
        enhanced_score = 0
        enhanced_total = 50
        
        # At least 1 portfolio project (10%)
        if Projects.objects.filter(user=obj.user).exists():
            enhanced_score += 10
        
        # Social media linked (10%)
        if SocialConnects.objects.filter(user=obj.user, connected=True).exists():
            enhanced_score += 10
        
        # Rate card setup (20%)
        if SocialRateCard.objects.filter(user=obj.user).exists():
            enhanced_score += 20
        
        # Bank account added + Aadhar/PAN KYC (10%)
        has_bank_account = BankAccountDetails.objects.filter(user=obj.user).exists()
        has_kyc = (hasattr(obj.user, 'pan_verification') and obj.user.pan_verification.is_pan_verified) or \
                  (hasattr(obj.user, 'aadhar_verification') and obj.user.aadhar_verification.is_aadhar_verified)
        
        if has_bank_account and has_kyc:
            enhanced_score += 10
        
        # Calculate total completion
        total_score = mandatory_score + enhanced_score
        total_possible = mandatory_total + enhanced_total
        
        completion_percentage = int((total_score / total_possible) * 100)
        
        return completion_percentage

class BrandProfileSerializer(serializers.ModelSerializer):
    """Serializer for basic brand profile information (Step 1)"""
    
    # All fields except brand_name are optional
    brand_logo = serializers.CharField(max_length=255, required=False, allow_blank=True)
    cin_certificate = serializers.CharField(max_length=255, required=False, allow_blank=True)
    brand_admin_name = serializers.CharField(required=False, allow_blank=True)
    industry_type = serializers.CharField(required=False, allow_blank=True)
    state = serializers.CharField(required=False, allow_blank=True)
    city = serializers.CharField(required=False, allow_blank=True)
    gst_number = serializers.CharField(required=False, allow_blank=True)
    company_name_on_cin = serializers.CharField(required=False, allow_blank=True)
    cin_number = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = BrandProfile
        fields = ['id', 'brand_logo', 'brand_name', 'brand_admin_name', 'industry_type', 'state', 'city',
                  'gst_number', 'company_name_on_cin', 'cin_number', 'cin_certificate']

    def validate_gst_number(self, value):
        if value:
            pattern = r'^\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}$'
            if not re.match(pattern, value):
                raise serializers.ValidationError("Enter a valid GST number")
        return value

    def validate_brand_name(self, value):
        if not value or len(value.strip()) < 3:
            raise serializers.ValidationError("Brand name must be at least 3 characters long")
        return value.strip()

    def validate_brand_admin_name(self, value):
        if value and len(value.strip()) < 3:
            raise serializers.ValidationError("Brand admin name must be at least 3 characters long")
        return value.strip() if value else value

    def validate_brand_logo(self, value):
        if value and len(value.strip()) > 255:
            raise serializers.ValidationError("Brand logo URL is too long")
        return value.strip() if value else value

    def validate_cin_certificate(self, value):
        if value and len(value.strip()) > 255:
            raise serializers.ValidationError("CIN certificate URL is too long")
        return value.strip() if value else value



# class BrandProfileSerializer(serializers.ModelSerializer):
#     """Serializer for basic brand profile information (Step 1)"""
#     brand_logo = serializers.CharField(max_length=255, required=False, allow_blank=True)
#     cin_certificate = serializers.CharField(max_length=255, required=False, allow_blank=True)
#     brand_admin_name = serializers.CharField(required=True)  # Make it required in serializer
#     industry_type = serializers.CharField(required=False, allow_blank=True)  # Make it optional
    
#     class Meta:
#         model = BrandProfile
#         fields = ['id', 'brand_logo', 'brand_name', 'brand_admin_name', 'industry_type', 'state', 'city', 
#                  'gst_number', 'company_name_on_cin', 'cin_number', 'cin_certificate']

#     def validate_gst_number(self, value):
#         if value:
#             pattern = r'^\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}$'
#             if not re.match(pattern, value):
#                 raise serializers.ValidationError("Enter a valid GST number")
#         return value

#     def validate_brand_name(self, value):
#         if not value or len(value.strip()) < 3:
#             raise serializers.ValidationError("Brand name must be at least 3 characters long")
#         return value.strip()
        
#     def validate_brand_admin_name(self, value):
#         if not value or len(value.strip()) < 3:
#             raise serializers.ValidationError("Brand admin name must be at least 3 characters long")
#         return value.strip()
        

#     def validate_brand_logo(self, value):
#         if value and len(value.strip()) > 255:
#             raise serializers.ValidationError("Brand logo URL is too long")
#         return value.strip() if value else value

#     def validate_cin_certificate(self, value):
#         if value and len(value.strip()) > 255:
#             raise serializers.ValidationError("CIN certificate URL is too long")
#         return value.strip() if value else value

class BrandAdminSerializer(serializers.ModelSerializer):
    """Serializer for brand admins (Step 2)"""
    
    class Meta:
        model = BrandAdmin
        fields = ['id', 'name', 'email', 'phone']
        read_only_fields = ['id']

    def validate_email(self, value):
        if not value:
            raise serializers.ValidationError("Email is required")
            
        # Convert email to lowercase
        value = value.lower()
        
        # Get current brand profile from context
        brand_profile = self.context.get('brand_profile')
        
        # Check if email already exists for this brand
        if BrandAdmin.objects.filter(brand=brand_profile, email=value).exclude(id=getattr(self.instance, 'id', None)).exists():
            raise serializers.ValidationError("The email already exists.")
            
        # Check if email exists in User table
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("The email already exists.")
            
        return value

    def validate_phone(self, value):
        if not re.match(r'^\d{10}$', value):
            raise serializers.ValidationError("Enter a valid 10-digit mobile number")
            
        # Get current brand profile from context
        brand_profile = self.context.get('brand_profile')
        
        # Check if phone already exists for this brand
        if BrandAdmin.objects.filter(brand=brand_profile, phone=value).exclude(id=getattr(self.instance, 'id', None)).exists():
            raise serializers.ValidationError("The phone number already exists.")
            
        # Check if phone exists in User table
        if User.objects.filter(mobile_number=value).exists():
            raise serializers.ValidationError("The phone number already exists.")
            
        return value

    def validate(self, data):
        request = self.context.get('request')
        brand_profile = self.context.get('brand_profile')
        
        if not self.instance:  # Creating new admin
            # Check if we're exceeding the limit of admins (max 4)
            existing_admins_count = BrandAdmin.objects.filter(brand=brand_profile).count()
            if existing_admins_count >= 4:  # Maximum 4 admins
                raise serializers.ValidationError("Maximum of 4 admins allowed")
        
        return data

    def create(self, validated_data):
        brand_profile = self.context.get('brand_profile')
        
        # Create User first
        user = User.objects.create(
            email=validated_data['email'],
            mobile_number=validated_data['phone'],
            username=validated_data['email'],  # Using email as username
            user_type='B',  # Brand type
            is_admin_user=True,  # Mark as admin user
            is_email_verified=True,  # Since they're added by brand
            is_mobile_verified=True  # Since they're added by brand
        )
        
        # Create BrandAdmin and link it to the User
        brand_admin = BrandAdmin.objects.create(
            brand=brand_profile,
            user=user,
            name=validated_data['name'],
            email=validated_data['email'],
            phone=validated_data['phone']
        )
        
        return brand_admin

    def update(self, instance, validated_data):
        # Update the User if it exists
        if instance.user:
            user = instance.user
            user.email = validated_data.get('email', user.email)
            user.mobile_number = validated_data.get('phone', user.mobile_number)
            user.username = validated_data.get('email', user.email)
            user.save()
        
        # Update the BrandAdmin
        instance.name = validated_data.get('name', instance.name)
        instance.email = validated_data.get('email', instance.email)
        instance.phone = validated_data.get('phone', instance.phone)
        instance.save()
        
        return instance

class BrandProfileRetrieveSerializer(serializers.ModelSerializer):
    """Serializer for retrieving the complete brand profile with all steps"""
    step_1 = serializers.SerializerMethodField()
    step_2 = serializers.SerializerMethodField()
    step_3 = serializers.SerializerMethodField()
    step_4 = serializers.SerializerMethodField()
    step_5 = serializers.SerializerMethodField()
    profile_completion = serializers.SerializerMethodField()
    
    class Meta:
        model = BrandProfile
        fields = ['id', 'step_1', 'step_2', 'step_3', 'step_4', 'step_5', 'profile_completion']

    def get_step_1(self, obj):
        """Brand details"""
        return {
            'brand_logo': obj.brand_logo.name if obj.brand_logo else None,
            'brand_name': obj.brand_name,
            'brand_admin_name': obj.brand_admin_name,
            'industry_type': obj.industry_type,
            'state': obj.state,
            'city': obj.city,
            'gst_number': obj.gst_number,
            'company_name_on_cin': obj.company_name_on_cin,
            'cin_number': obj.cin_number,
            'cin_certificate': obj.cin_certificate.name if obj.cin_certificate else None
        }

    def get_step_2(self, obj):
        """Admin details"""
        admins = BrandAdmin.objects.filter(brand=obj)
        return {
            'admins': BrandAdminSerializer(admins, many=True).data
        }

    def get_step_3(self, obj):
        """Projects"""
        projects = Projects.objects.filter(user=obj.user).order_by('-created_at')
        return {
            'projects': ProjectSerializer(projects, many=True).data
        }

    def get_step_4(self, obj):
        """Social connects"""
        social_connects = SocialConnects.objects.filter(user=obj.user)
        return {
            'social_connects': SocialConnectSerializer(social_connects, many=True).data
        }

    def get_step_5(self, obj):
        """Bank account details"""
        try:
            bank_account = BankAccountDetails.objects.get(user=obj.user)
            return {
                'bank_account': BankAccountDetailsSerializer(bank_account).data
            }
        except BankAccountDetails.DoesNotExist:
            return {
                'bank_account': None
            }
    
    def get_profile_completion(self, obj):
        """Calculate brand profile completion percentage based on new requirements"""
        completion_percentage = 0
        
        # BASIC (REQUIRED) (70%)
        basic_score = 0
        basic_total = 70
        
        # Primary Admin email & phone verification (10%)
        if obj.user.is_email_verified and obj.user.is_mobile_verified:
            basic_score += 10
        
        # Brand logo (10%)
        if obj.brand_logo:
            basic_score += 10
        
        # Brand name (10%)
        if obj.brand_name and len(obj.brand_name.strip()) >= 3:
            basic_score += 10
        
        # Brand Primary Admin Name (10%)
        if obj.brand_admin_name and len(obj.brand_admin_name.strip()) >= 3:
            basic_score += 10
        
        # Industry Type (10%)
        if obj.industry_type and len(obj.industry_type.strip()) > 0:
            basic_score += 10
        
        # Location (city/state) (5%)
        if obj.city and obj.state:
            basic_score += 5
        
        # GST number (5%)
        if obj.gst_number and len(obj.gst_number.strip()) > 0:
            basic_score += 5
        
        # Company name & CIN details, certificate upload (10%)
        has_cin_details = obj.company_name_on_cin and obj.cin_number and obj.cin_certificate
        if has_cin_details:
            basic_score += 10
        
        # ENHANCED (RECOMMENDED) (30%)
        enhanced_score = 0
        enhanced_total = 30
        
        # At least 1 past project (10%)
        if Projects.objects.filter(user=obj.user).exists():
            enhanced_score += 10
        
        # Bank account linked + UPI/KYC (10%)
        has_bank_account = BankAccountDetails.objects.filter(user=obj.user).exists()
        has_kyc = (hasattr(obj.user, 'pan_verification') and obj.user.pan_verification.is_pan_verified) or \
                  (hasattr(obj.user, 'aadhar_verification') and obj.user.aadhar_verification.is_aadhar_verified)
        
        if has_bank_account and has_kyc:
            enhanced_score += 10
        
        # Social links connected (Facebook, IG, LinkedIn, Twitter, YouTube) (10%)
        social_platforms = ['Facebook', 'Instagram', 'LinkedIn', 'Twitter', 'YouTube']
        connected_social_platforms = SocialConnects.objects.filter(
            user=obj.user,
            connected=True,
            platform__name__in=social_platforms
        ).count()
        
        if connected_social_platforms >= 1:  # At least one social platform connected
            enhanced_score += 10
        
        # Calculate total completion
        total_score = basic_score + enhanced_score
        total_possible = basic_total + enhanced_total
        
        completion_percentage = int((total_score / total_possible) * 100)
        
        return completion_percentage

class SocialMediaPostSerializer(serializers.ModelSerializer):
    class Meta:
        model = SocialMediaPost
        fields = [
            'id', 'post_id', 'post_url', 'post_type',
            'reach', 'engagement', 'likes', 'comments',
            'shares', 'posted_at', 'created_at'
        ]

class SocialMediaAnalyticsSerializer(serializers.ModelSerializer):
    posts = SocialMediaPostSerializer(many=True, read_only=True)
    platform_name = serializers.CharField(source='social_connect.platform.name', read_only=True)
    
    class Meta:
        model = SocialMediaAnalytics
        fields = [
            'id', 'platform_name', 'followers_count', 'following_count',
            'posts_count', 'average_reach', 'average_engagement',
            'created_at', 'posts'
        ]
class UpdateContactSerializer(serializers.ModelSerializer):
    mobile_number = serializers.CharField(
        max_length=10,
        validators=[RegexValidator(regex=r'^\d{10}$', message='Enter a valid 10-digit mobile number.')],
        required=False
    )
    email = serializers.EmailField(required=False)

    class Meta:
        model = User
        fields = ['mobile_number', 'email']

    def validate_mobile_number(self, value):
        if value:
            # Check if mobile number already exists for another user
            if User.objects.exclude(id=self.instance.id).filter(mobile_number=value).exists():
                raise serializers.ValidationError("This mobile number is already registered")
        return value

    def validate_email(self, value):
        if value:
            # Check if email already exists for another user
            if User.objects.exclude(id=self.instance.id).filter(email=value).exists():
                raise serializers.ValidationError("This email is already registered")
        return value

    def validate(self, data):
        if not data.get('mobile_number') and not data.get('email'):
            raise serializers.ValidationError("At least one field (mobile_number or email) must be provided")
        return data

    def update(self, instance, validated_data):
        # Reset verification status when contact details are updated
        if 'mobile_number' in validated_data:
            instance.mobile_number = validated_data['mobile_number']
            instance.is_mobile_verified = False
        if 'email' in validated_data:
            instance.email = validated_data['email']
            instance.is_email_verified = False
        instance.save()
        return instance


class FCMTokenSerializer(serializers.ModelSerializer):
    class Meta:
        model = Token
        fields = ['device_id', 'registration_id']

class IndustryTypeSerializer(serializers.ModelSerializer):
    """Serializer for industry types"""
    class Meta:
        model = IndustryType
        fields = ['id', 'name']


class NotificationSerializer(serializers.ModelSerializer):
    title = OptionChoiceField(choices=NotificationModuleChoice.choices,required=False)
    class Meta:
        model = Notification
        fields = ['id', 'user','title', 'module', 'is_read', 'message', 'extra_info', 'is_triggered', 'created_at']


class BrandNameSerializer(serializers.ModelSerializer):
    class Meta:
        model = BrandProfile
        fields = ['id', 'brand_name']


# class SocialMediaPlatformSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = SocialMediaPlatform
#         fields = ['name', 'icon']
# class ExpertiseAreaSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = ExpertiseArea
#         fields = ['id', 'name']
class HelperProjectSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()
    video = serializers.SerializerMethodField()
    thumbnail = serializers.SerializerMethodField()
    social_tags = serializers.SerializerMethodField()
    category_tags = serializers.StringRelatedField(many=True)

    class Meta:
        model = Projects
        fields = [
            "id", "title", "description", "image", "video", "thumbnail",
            "social_tags", "category_tags", "created_at",
        ]

    def get_image(self, obj):
        return obj.image.name if obj.image else None

    def get_video(self, obj):
        return obj.video.name if obj.video else None

    def get_thumbnail(self, obj):
        return obj.thumbnail.name if obj.thumbnail else None

    def get_social_tags(self, obj):
        result = []
        user = obj.user

        for platform in obj.social_tags.all():
            platform_categories = platform.platforms.all()  # preloaded by related_name

            deliverables = []
            for category in platform_categories:
                rate = next(
                    (r for r in user.social_rate_cards.all() if r.platform_category_id == category.id),
                    None
                )
                if rate:
                    deliverables.append({
                        "category": category.name,
                        "min_price": float(rate.min_price),
                        "max_price": float(rate.max_price),
                    })

            result.append({
                "platform": platform.name,
                "platform_icon": platform.icon.name if platform.icon else None,
                "deliverables": deliverables,
            })

        return result

    
class TalentListSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='talent_profile.name')
    profile_pic = serializers.SerializerMethodField()
    city = serializers.CharField(source='talent_profile.city')
    state = serializers.CharField(source='talent_profile.state')
    expertise_areas = serializers.StringRelatedField(source='talent_profile.expertise_areas', many=True)
    rating = serializers.SerializerMethodField()
    social_media_tags = serializers.SerializerMethodField()
    match_score = serializers.SerializerMethodField()
    is_talent_bookmarked = serializers.SerializerMethodField()
    talent_id = serializers.CharField(source='talent_profile.id', read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'name', 'city', 'state',
            'profile_pic', 'expertise_areas', 'rating', 'social_media_tags', 'match_score','is_talent_bookmarked','talent_id'
        ]

    def get_rating(self, obj):
        return None

    def get_profile_pic(self, obj):
        talent_profile = getattr(obj, 'talent_profile', None)
        if talent_profile and talent_profile.profile_pic:
            return talent_profile.profile_pic.name
        return None

    def get_social_media_tags(self, obj):
        tags = set()
        for project in obj.projects.all():
            for tag in project.social_tags.all():
                tags.add((tag.name, tag.icon.name if tag.icon else None))
        return [{'name': name, 'icon': icon} for name, icon in tags]

    def get_match_score(self, obj):
        request = self.context.get('request')
        brand = request.user if request and request.user.user_type == UserTypeChoices.BRAND.value[0] else None
        if not brand:
            return {"best_match": 0.0, "average_match": 0.0}
        from talent_trade.services.match_engine import MatchEngine
        return MatchEngine.get_user_campaign_score(obj, brand)
    
    def get_is_talent_bookmarked(self, obj):
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False
        return BookmarkTalent.objects.filter(brand=request.user, talent=obj).exists()


class TalentDetailsSerializer(TalentListSerializer):
    languages = serializers.StringRelatedField(source='talent_profile.languages', many=True)
    projects = HelperProjectSerializer(many=True, read_only=True)

    class Meta(TalentListSerializer.Meta):
        fields = TalentListSerializer.Meta.fields + [
            'email', 'mobile_number', 'languages', 'projects',
        ]


class BookmarkTalentSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookmarkTalent
        fields = ['id', 'brand', 'talent', 'created_at']

