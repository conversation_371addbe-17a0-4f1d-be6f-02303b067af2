from enum import Enum

from django.db.models import TextChoices


class ChoiceEnum(Enum):
    @classmethod
    def get_value(cls, member):
        return cls[member].value[0]

    @classmethod
    def get_choices(cls):
        return tuple(x.value for x in cls)

    @classmethod
    def get_values(cls):
        return [choice.value[0] for choice in cls]

    @classmethod
    def get_full_values(cls):
        return [choice.value[1] for choice in cls]

    @classmethod
    def get_full_value_from_short(cls, short_code):
        for choice in cls:
            if choice.value[0] == short_code:
                return choice.value[1]
        return None

class UserTypeChoices(ChoiceEnum):
    TALENT = ("T", "Talent")
    BRAND = ("B", "Brand")

class GenderChoices(ChoiceEnum):
    MALE = ("M", "Male")
    FEMALE = ("F", "Female")
    OTHER = ("O", "Other")

class DurationTypeChoices(ChoiceEnum):
    DAYS = ("D", "Days")
    WEEKS = ("W", "Weeks")
    MONTHS = ("M", "Months")