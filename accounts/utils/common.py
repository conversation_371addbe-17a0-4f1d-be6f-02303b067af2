from rest_framework import serializers

class CustomValidation:
    @staticmethod
    def raise_validation_error(message):
        """
        Raise a validation error with consistent format
        """
        raise serializers.ValidationError({
            "message": message
        })

class BaseSerializer(serializers.ModelSerializer):
    """
    Base serializer with common validation handling
    """
    def validate(self, attrs):
        try:
            return super().validate(attrs)
        except serializers.ValidationError as e:
            if hasattr(e, 'detail') and isinstance(e.detail, dict) and 'message' in e.detail:
                raise
            # Convert any other validation error to our standard format
            CustomValidation.raise_validation_error(str(e.detail[0] if isinstance(e.detail, list) else e.detail)) 