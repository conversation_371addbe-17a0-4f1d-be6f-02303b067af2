import random
import string
from django.core.mail import send_mail
from django.conf import settings
import os
from dotenv import load_dotenv

load_dotenv()

def generate_otp(length=6):
    """Generate a random numeric OTP of specified length."""
    return ''.join(random.choices(string.digits, k=length))

def is_master_otp(otp):
    """Check if the provided OTP matches the master OTP from environment variables."""
    master_otp = os.getenv('MASTER_OTP')
    return master_otp and otp == master_otp 