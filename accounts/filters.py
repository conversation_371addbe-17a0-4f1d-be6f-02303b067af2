import django_filters
from accounts.models import User
from django.db.models import Q


class TalentFilter(django_filters.FilterSet):
    state = django_filters.CharFilter(method='filter_state')
    city = django_filters.CharFilter(method='filter_city')
    category = django_filters.CharFilter(method='filter_category')
    min_price = django_filters.NumberFilter(method='filter_min_price')
    max_price = django_filters.NumberFilter(method='filter_max_price')
    language = django_filters.CharFilter(method='filter_language')  # New
    gender = django_filters.CharFilter(method='filter_gender')      # New
    platform = django_filters.CharFilter(method='filter_platform')  # New

    class Meta:
        model = User
        fields = []

    def filter_state(self, queryset, name, value):
        state_list = [x.strip() for x in value.split(',') if x.strip()]
        if state_list:
            queryset = queryset.filter(talent_profile__state__in=state_list)
        return queryset

    def filter_city(self, queryset, name, value):
        city_list = [x.strip() for x in value.split(',') if x.strip()]
        if city_list:
            queryset = queryset.filter(talent_profile__city__in=city_list)
        return queryset

    def filter_category(self, queryset, name, value):
        category_ids = [int(cid.strip()) for cid in value.split(',') if cid.strip().isdigit()]
        if category_ids:
            queryset = queryset.filter(talent_profile__expertise_areas__id__in=category_ids)
        return queryset

    def filter_min_price(self, queryset, name, value):
        return queryset.filter(social_rate_cards__min_price__gte=value)

    def filter_max_price(self, queryset, name, value):
        return queryset.filter(social_rate_cards__max_price__lte=value)
    
    def filter_language(self, queryset, name, value):
        language_ids = [int(lid) for lid in value.split(',') if lid.isdigit()]
        return queryset.filter(talent_profile__languages__id__in=language_ids)
    
    def filter_gender(self, queryset, name, value):
        gender_values = [x.strip().upper() for x in value.split(',') if x.strip().upper() in dict(GenderChoices.get_choices())]
        if gender_values:
            return queryset.filter(talent_profile__gender__in=gender_values)
        return queryset
    
    def filter_platform(self, queryset, name, value):
        platform_ids = [int(pid.strip()) for pid in value.split(',') if pid.strip().isdigit()]
        if platform_ids:
            queryset = queryset.filter(projects__social_tags__id__in=platform_ids).distinct()
        return queryset