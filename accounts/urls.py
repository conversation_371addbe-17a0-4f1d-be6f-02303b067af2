from django.urls import path
from .views import *

urlpatterns = [
    path('send-otp/', SendOTPView.as_view(), name='send-otp'),
    path('verify-otp/', VerifyOTPView.as_view(), name='verify-otp'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('signup/', SignupView.as_view(), name='signup'),
    path('soft-delete/', SoftDeleteUserView.as_view(), name='soft-delete'),
    path('languages/', LanguageView.as_view({'get':'list'}), name='languages'),
    path('expertise-areas/', ExpertiseAreaView.as_view(), name='expertise-areas'),
    path(
        "generate-presigned-url/",
        GeneratePresignedUrl.as_view(),
        name="generate_presigned_url",
    ),
    path('social-media-tags/',SocialMediaRateListView.as_view(),name='social-media-tags'),
    path('talent-profile/', TalentProfileView.as_view(), name='talent-profile-step'),
    path('brand-profile/', BrandProfileView.as_view(), name='brand-profile'),
    path('industry-types/', IndustryTypesView.as_view(), name='industry-types'),
    path('analytics/', SocialMediaAnalyticsView.as_view(), name='social-media-analytics'),
    path('analytics/<int:platform_id>/', SocialMediaAnalyticsView.as_view(), name='platform-analytics'),
    path('update-contact/', UpdateContactView.as_view(), name='update-contact'),
    path('connect-social/', SocialMediaConnectView.as_view(), name='connect-social'),
    path('connect-social/<int:platform_id>/', SocialMediaConnectView.as_view(), name='disconnect-social'),
    path('generate-aadhar-otp/', GenerateAadharOTPView.as_view(), name='generate-aadhar-otp'),
    path('aadhaar-verify-otp/', AadhaarOTPVerifyAPIView.as_view(), name='aadhaar-verify-otp'),
    path('verify-pan/', VerifyPANAPIView.as_view(), name='verify-pan'),
    path('fcm_token/',FCMTokenViewSet.as_view({'post':'create'}),name='fcm_token'),
    path('notifications/', NotificationListView.as_view({'get':'list',"patch":"patch"}), name='notification-list'),
    path('notifications/<int:id>/', NotificationListView.as_view({'get':'retrieve',"patch": "patch"}), name='notification-detail'),
    path('brand-names/', BrandNameListView.as_view({'get':'list'}), name='brand-names-list'),
    path('discover-talent/',TalentListViewSet.as_view({'get':'list'}),name='discover-talent'),
    path('discover-talent/<int:id>/',TalentListViewSet.as_view({'get':'retrieve'}),name='discover-talent'),
    path('bookmark-talent/<int:talent_id>/', TalentBookmarkViewSet.as_view({'post': 'create', 'delete': 'destroy'})),
    path('bookmark-talent/', TalentBookmarkViewSet.as_view({'get': 'list'})),
    path('dashboard/', DashboardAPIView.as_view(), name='dashboard'),
    path('validate-contact/', ContactValidationView.as_view(), name='validate-contact'),


]
