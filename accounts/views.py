from django.shortcuts import render
from accounts.choices import UserTypeChoices
from accounts.filters import Talent<PERSON>ilter
from accounts.helpers import Presigned<PERSON><PERSON><PERSON>elper
from rest_framework import status
from rest_framework.views import APIView
from django.utils import timezone
from django.db.models import Q

from campaigns.pagination import CustomPagination
from talent_trade.middleware.authentication import TokenAuthentication
from .models import BookmarkTalent, Notification, Token
from django.contrib.auth import authenticate

from talent_trade.services.email_services import EmailService
from talent_trade.services.surepass import generate_aadhar_otp, update_kyc_status, verify_aadhar_otp, verify_pan
from talent_trade.utils.choices import KYCStatusChoices
from .serializers import (
    BookmarkTalentSerializer,
    BrandNameSerializer,
    NotificationSerializer,
    OTPRequestSerializer, 
    OTPVerificationSerializer,
    SocialMediaPlatformSerializer,
    TalentDetailsSerializer,
    TalentListSerializer, 
    UserSerializer,
    SignupSerializer,
    LanguageSerializer,
    ExpertiseAreaSerializer,
    TalentProfileSerializer,
    TalentProfileStepSerializer,
    ProjectSerializer,
    SocialConnectSerializer,
    TalentProfileLanguagesSerializer,
    SocialRateCardSerializer,
    BankAccountDetailsSerializer,
    TalentProfileRetrieveSerializer,
    BrandProfileSerializer,
    BrandAdminSerializer,
    BrandProfileRetrieveSerializer,
    UpdateContactSerializer,
    SocialMediaAnalyticsSerializer,
    IndustryTypeSerializer,
    ContactValidationSerializer
)
from .models import OTP, User, Language, ExpertiseArea,TalentProfile, BankAccountDetails,SocialMediaPlatform, Projects,BrandProfile,BrandAdmin, SocialMediaAnalytics, SocialConnects, SocialRateCard, IndustryType, Role, UserRole
from .models import OTP, AadhaarClient, PANVerification, User, Language, ExpertiseArea,TalentProfile, BankAccountDetails,SocialMediaPlatform, Projects,BrandProfile,BrandAdmin
from .utils import generate_otp, is_master_otp
from rest_framework.permissions import AllowAny,IsAuthenticated
from .responses import Responses
from datetime import timedelta
from rest_framework import viewsets
from rest_framework.generics import GenericAPIView
from rest_framework.exceptions import ValidationError
from accounts.services.analytics_service import AnalyticsService
from rest_framework.authentication import get_authorization_header
from rest_framework.viewsets import ModelViewSet
from rest_framework.exceptions import NotFound, ValidationError
# Create your views here.

class SoftDeleteUserView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        user = request.user
        user.is_active = False
        user.save()
        
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Your account has been deactivated successfully.',
            data={}
        )

class SendOTPView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = OTPRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Responses.handle_validation_error(serializer.errors)
        
        email = serializer.validated_data.get('email')
        mobile_number = serializer.validated_data.get('mobile_number')
        is_signup = serializer.validated_data.get('is_signup', False)
        user_type = serializer.validated_data.get('user_type')

        # Check if user exists and is active
        user = None
        if email:
            user = User.objects.filter(email=email).first()
        elif mobile_number:
            user = User.objects.filter(mobile_number=mobile_number).first()

        if user and not user.is_active:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Your account has been deactivated. Please contact <NAME_EMAIL> to reactivate it.',
                data={}
            )

        # Check if email/phone exists in User table
        if email:
            user_exists = User.objects.filter(email=email).exists()
            if user_exists and is_signup:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='This email is already registered. Please login instead.',
                    data={'email': email}
                )
            elif not user_exists and not is_signup:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message='Email not registered. Please create an account',
                    data={'email': email}
                )
            
            # Additional validation for user_type mismatch
            if user_exists and user_type:
                existing_user = User.objects.get(email=email)
                if existing_user.user_type != user_type:
                    user_type_display = dict(UserTypeChoices.get_choices()).get(existing_user.user_type, existing_user.user_type)
                    requested_user_type_display = dict(UserTypeChoices.get_choices()).get(user_type, user_type)
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message=f'This email is already registered as {user_type_display}. You cannot use it as {requested_user_type_display}.',
                        data={'email': email, 'existing_user_type': existing_user.user_type}
                    )
        elif mobile_number:
            user_exists = User.objects.filter(mobile_number=mobile_number).exists()
            if user_exists and is_signup:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='This mobile number is already registered. Please login instead.',
                    data={'mobile_number': mobile_number}
                )
            elif not user_exists and not is_signup:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Mobile number not registered. Please create an account',
                    data={'mobile_number': mobile_number}
                )
            
            # Additional validation for user_type mismatch
            if user_exists and user_type:
                existing_user = User.objects.get(mobile_number=mobile_number)
                if existing_user.user_type != user_type:
                    user_type_display = dict(UserTypeChoices.get_choices()).get(existing_user.user_type, existing_user.user_type)
                    requested_user_type_display = dict(UserTypeChoices.get_choices()).get(user_type, user_type)
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message=f'This mobile number is already registered as {user_type_display}. You cannot use it as {requested_user_type_display}.',
                        data={'mobile_number': mobile_number, 'existing_user_type': existing_user.user_type}
                    )

        # Check for existing unused and valid OTP
        existing_otp = OTP.objects.filter(
            Q(email=email) if email else Q(phone=mobile_number),
            is_used=False,
            is_verified=False
        ).order_by('-created_at').first()

        if existing_otp:
            # Refresh the expiry time
            existing_otp.expires_at = timezone.now() + timedelta(minutes=10)
            existing_otp.save()
            otp = existing_otp.otp
        else:
            otp = generate_otp()
            OTP.objects.create(
                email=email,
                phone=mobile_number,
                otp=otp
            )

        if email:
            email_sent, error = EmailService.send_email(
            subject="Your OTP Code",
            message=f"Your One Time Password is: {otp}",
            recipient_list=[email],
            fail_silently=False
        )
            if not email_sent:
                print(f"[ERROR] Failed to send OTP email: {error}")
                
        response_data = {
            'email': email,
            'is_signup': is_signup
        } if email else {
            'mobile_number': mobile_number,
            'is_signup': is_signup
        }

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='OTP sent successfully',
            data=response_data
        )

class VerifyOTPView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if not serializer.is_valid():
            return Responses.handle_validation_error(serializer.errors)

        email = serializer.validated_data.get('email')
        mobile_number = serializer.validated_data.get('mobile_number')
        otp_code = serializer.validated_data.get('otp')

        device_id = request.data.get('device_id')
        registration_id = request.data.get('registration_id')

        # Check for master OTP first
        is_master = is_master_otp(otp_code)
        otp_record = None

        if is_master:
            # Create a dummy verified OTP record for audit
            otp_record = OTP.objects.create(
                email=email if email else None,
                phone=mobile_number if mobile_number else None,
                otp=otp_code,
                is_verified=True,
                is_used=True  # optional
            )
            otp_record.otp_verified = True
            otp_record.save()
        
        else:
            # If not master OTP, verify against stored OTP
            otp_record = OTP.objects.filter(
                Q(email=email) if email else Q(phone=mobile_number),
                otp=otp_code,
                is_used=False,
                is_verified=False
            ).order_by('-created_at').first()

            if not otp_record:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Incorrect OTP. Please try again.',
                    data={}
                )

            # Mark OTP as verified
            otp_record.is_verified = True
            otp_record.save()

        # Find or update user
        user = None
        if email:
            user = User.objects.filter(email=email).first()
            if user:
                user.is_email_verified = True
                user.save()
        elif mobile_number:
            user = User.objects.filter(mobile_number=mobile_number).first()
            if user:
                user.is_mobile_verified = True
                user.save()

        response_data = {
            "is_user_exists": user is not None,
            "is_master_otp": is_master,
            "profile_created": TalentProfile.objects.filter(user=user).exists() if user else False
        }

        if user:
            # Direct token creation logic (exactly like SignupView)
            if device_id:
                Token.objects.filter(user=user, device_id=device_id).delete()

            token = Token.objects.create(
                user=user,
                device_id=device_id,
                registration_id=registration_id
            )

            user_data = UserSerializer(user).data
            user_data["token"] = token.key
            response_data["user"] = user_data

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Verification successful.",
            data=response_data
        )


        # if user:
        #     token, _ = Token.objects.get_or_create(user=user)
        #     user_data = UserSerializer(user).data
        #     user_data['token'] = token.key
        #     response_data['user'] = user_data

        # return Responses.success_response(
        #     status=status.HTTP_200_OK,
        #     message='Verification successful.',
        #     data=response_data
        # )

# class LogoutView(APIView):
#     def post(self, request):
#         try:
#             request.user.auth_token.delete()
#             return Responses.success_response(
#                 status=status.HTTP_200_OK,
#                 message='Successfully logged out'
#             )
#         except Exception as e:
#             return Responses.error_response(
#                 status=status.HTTP_400_BAD_REQUEST,
#                 message='Something went wrong',
#                 data=str(e)
#             )
class LogoutView(APIView):
    def post(self, request):
        auth_header = get_authorization_header(request).decode('utf-8')
        token_key = None

        if auth_header.startswith("Token "):
            token_key = auth_header.split(" ")[1]

        if not token_key:
            return Responses.error_response(
                status=status.HTTP_401_UNAUTHORIZED,
                message="Authorization token is missing or invalid."
            )

        try:
            token = Token.objects.get(key=token_key)
            token.delete()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message="Successfully logged out"
            )
        except Token.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Token not found or already deleted."
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message="Something went wrong",
                data=str(e)
            )

class SignupView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = SignupSerializer(data=request.data)
        if not serializer.is_valid():
            return Responses.handle_validation_error(serializer.errors)

        email = serializer.validated_data.get('email')
        mobile_number = serializer.validated_data.get('mobile_number')

        device_id = request.data.get('device_id')
        registration_id = request.data.get('registration_id')

        # Create user
        user = serializer.save()

        # Delete old OTPs
        OTP.objects.filter(Q(email=email) | Q(phone=mobile_number)).delete()

        # Delete existing token for this user + device_id
        if device_id:
            Token.objects.filter(user=user, device_id=device_id).delete()

        # Create a new token
        token = Token.objects.create(
            user=user,
            device_id=device_id,
            registration_id=registration_id,
        )

        user_data = UserSerializer(user).data
        user_data['token'] = token.key

        return Responses.success_response(
            status=status.HTTP_201_CREATED,
            message='User registered successfully',
            data=user_data
        )

class LanguageView(ModelViewSet):
    permission_classes = [AllowAny]
    serializer_class = LanguageSerializer
    queryset = Language.objects.filter(is_active=True)
    search_fields = ('value',)

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Languages retrieved successfully',
            data=response.data
        )

class ExpertiseAreaView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        expertise_areas = ExpertiseArea.objects.filter(is_active=True)
        serializer = ExpertiseAreaSerializer(expertise_areas, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Expertise areas retrieved successfully',
            data=serializer.data
        )    

class GeneratePresignedUrl(GenericAPIView):
    def post(self, request, *args, **kwargs):
        request_data = request.data
        validator = PresignedUrlHelper(request_data)

        try:
            validator.validate_request_data()
        except ValidationError as e:
            return Responses.handle_validation_error(e.detail)

        presigned_urls = validator.generate_presigned_urls(request.user.id)

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Presigned urls generated",
            data=presigned_urls
        )

class SocialMediaRateListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        platforms = SocialMediaPlatform.objects.all()
        serializer = SocialMediaPlatformSerializer(platforms, many=True, context={"request": request})
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Social media tags fetch susccessfully",
            data=serializer.data)
    
    
class TalentProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            profile = TalentProfile.objects.get(user=request.user)
            serializer = TalentProfileRetrieveSerializer(profile)
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Talent profile retrieved successfully',
                data=serializer.data
            )
        except TalentProfile.DoesNotExist:
            # Return empty data structure for new profiles
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='No talent profile found',
                data={
                    'id': None,
                    'step_1': {
                    },
                    'step_2': {
                        'expertise_areas': []
                    },
                    'step_3': {
                        'projects': []
                    },
                    'step_4': {
                        'social_connects': []
                    },
                    'step_5': {
                        'languages': []
                    },
                    'step_6': {
                        'rate_cards': []
                    },
                    'step_7': {
                        'bank_accounts': None
                    },
                    'profile_completion': 0
                }
            )
    
    def patch(self, request):
        try:
            profile = TalentProfile.objects.get(user=request.user)
        except TalentProfile.DoesNotExist:
            # Create a new profile if it doesn't exist
            profile = TalentProfile.objects.create(user=request.user)

        step = request.data.get('step')
        # Handle different steps
        if step == '1':
            return self.handel_step_1(request,profile)
        elif step == '2':
            return self.handel_step_2(request,profile)
        elif step == '3':
            return self.handel_step_3(request,profile)
        elif step == '4':
            return self.handel_step_4(request,profile)
        elif step == '5':
            return self.handel_step_5(request,profile)
        elif step == '6':
            return self.handel_step_6(request,profile)
        elif step == '7':
            return self.handel_step_7(request,profile)
        else:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Invalid step',
                data={}
            )

    def handel_step_1(self, request, profile):
        # Step 1: Basic information (name, gender, date_of_birth)
        # Check if user is skipping this step
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            # If skipping, just return success without validation
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 1 skipped',
                data={}
            )
        
        serializer = TalentProfileSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 1 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)
    
    def handel_step_2(self, request, profile):
        # Check if user is skipping this step
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            # If skipping, just return success without validation
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 2 skipped',
                data={}
            )
        
        serializer = TalentProfileStepSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 2 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)
    def handel_step_3(self, request, profile):
        # Step 3: Projects
        # Check if user is skipping this step
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            # If skipping, just return success without validation
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 3 skipped',
                data={}
            )
        
        # Handle project deletion
        project_to_delete = request.data.get('delete_project_id')
        if project_to_delete:
            try:
                project = Projects.objects.get(id=project_to_delete, user=request.user)
                project.delete()
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Project deleted successfully',
                    data={}
                )
            except Projects.DoesNotExist:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message=f'Project with id {project_to_delete} not found',
                    data={}
                )
        
        # Handle multiple projects
        projects_data = request.data.get('projects', [])
        if not isinstance(projects_data, list):
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Projects must be an array',
                data={}
            )

        saved_projects = []
        for project_data in projects_data:
            project_id = project_data.get('id')
            try:
                if project_id:
                    # Update existing project
                    project = Projects.objects.get(id=project_id, user=request.user)
                    serializer = ProjectSerializer(project, data=project_data, partial=True, context={'request': request})
                else:
                    # Create new project
                    serializer = ProjectSerializer(data=project_data, context={'request': request})
                
                if serializer.is_valid():
                    project = serializer.save()
                    saved_projects.append(serializer.data)
                else:
                    # If any project is invalid, return the error
                    return Responses.handle_validation_error(serializer.errors)
            except Projects.DoesNotExist:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message=f'Project with id {project_id} not found',
                    data={}
                )

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Step 3 completed successfully',
            data=saved_projects
        )
        
    def handel_step_4(self, request, profile):

        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            # If skipping, just return success without validation
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 5 skipped',
                data={}
            )
        # Step 4: Social connects
        serializer = SocialConnectSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='step 4 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)

    def handel_step_5(self, request, profile):
        # Step 5: Languages
        # Check if user is skipping this step
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            # If skipping, just return success without validation
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 5 skipped',
                data={}
            )
        
        serializer = TalentProfileLanguagesSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 5 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)
        
    def handel_step_6(self, request, profile):
        # Step 6: Social rate cards
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 6 skipped',
                data={}
            )
            
        rate_cards_data = request.data.get('rate_cards', [])
        if not isinstance(rate_cards_data, list):
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='rate_cards must be an array',
                data={}
            )

        saved_rate_cards = []
        for rate_card_data in rate_cards_data:
            rate_card_id = rate_card_data.get('id')
            
            try:
                if rate_card_id:
                    # Update existing rate card
                    rate_card = SocialRateCard.objects.get(
                        id=rate_card_id,
                        user=request.user
                    )
                    serializer = SocialRateCardSerializer(
                        rate_card,
                        data=rate_card_data,
                        partial=True,
                        context={'request': request}
                    )
                else:
                    # Create new rate card
                    serializer = SocialRateCardSerializer(
                        data=rate_card_data,
                        context={'request': request}
                    )
                
                if serializer.is_valid():
                    rate_card = serializer.save(user=request.user)
                    saved_rate_cards.append(serializer.data)
                else:
                    return Responses.handle_validation_error(serializer.errors)
            except SocialRateCard.DoesNotExist:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message=f'Rate card with id {rate_card_id} not found',
                    data={}
                )
            except Exception as e:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Error processing rate card',
                    data={'error': str(e)}
                )

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Step 6 completed successfully',
            data=saved_rate_cards
        )
        
    def handel_step_7(self, request, profile):
        # Step 7: Bank account details

        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            # If skipping, just return success without validation
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 5 skipped',
                data={}
            )
        try:
            bank_account = BankAccountDetails.objects.get(user=request.user)
            serializer = BankAccountDetailsSerializer(
                bank_account, 
                data=request.data, 
                partial=True, 
                context={'request': request}
            )
        except BankAccountDetails.DoesNotExist:
            serializer = BankAccountDetailsSerializer(
                data=request.data, 
                context={'request': request}
            )
        
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 7 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)

    def delete(self, request):
        """Delete bank account details"""
        try:
            bank_account = BankAccountDetails.objects.get(user=request.user)
            bank_account.delete()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Bank account details deleted successfully',
                data={}
            )
        except BankAccountDetails.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='No bank account details found',
                data={}
            )

class BrandProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            profile = BrandProfile.objects.get(user=request.user)
            serializer = BrandProfileRetrieveSerializer(profile)
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Brand profile retrieved successfully',
                data=serializer.data
            )
        except BrandProfile.DoesNotExist:
            # Return empty data structure for new profiles
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='No brand profile found',
                data={
                    'id': None,
                    'step_1': {},
                    'step_2': {
                        'admins': []
                    },
                    'step_3': {
                        'projects': []
                    },
                    'step_4': {
                        'social_connects': []
                    },
                    'step_5': {
                        'bank_account': None
                    },
                    'profile_completion': 0
                }
            )

    def patch(self, request):
        # Check if profile exists, if not create one
        try:
            profile = BrandProfile.objects.get(user=request.user)
        except BrandProfile.DoesNotExist:
            profile = BrandProfile.objects.create(user=request.user)

        step = request.data.get('step')
        # Handle different steps
        if step == '1':
            return self.handle_step_1(request, profile)
        elif step == '2':
            return self.handle_step_2(request, profile)
        elif step == '3':
            return self.handle_step_3(request, profile)
        elif step == '4':
            return self.handle_step_4(request, profile)
        elif step == '5':
            return self.handle_step_5(request, profile)
        else:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Invalid step',
                data={}
            )

    def handle_step_1(self, request, profile):
        # Step 1: Brand details
        # Check if user is skipping this step
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            # If skipping, just return success without validation
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 1 skipped',
                data={}
            )
        
        if profile:
            serializer = BrandProfileSerializer(profile, data=request.data, partial=True)
        else:
            serializer = BrandProfileSerializer(data=request.data)
            
        if serializer.is_valid():
            if not profile:
                # Create new profile
                profile = serializer.save(user=request.user)
            else:
                profile = serializer.save()
                
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 1 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)
    
    def handle_step_2(self, request, profile):
        # Step 2: Admin details
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 2 skipped',
                data={}
            )

        # Handle keep functionality
        keep = request.data.get('keep', False)
        admin_ids = request.data.get('admin_ids', [])  # Changed from admin_id to admin_ids list

        if keep and admin_ids:
            try:
                # Verify all admins exist and belong to this brand
                admins_to_keep = BrandAdmin.objects.filter(
                    id__in=admin_ids,
                    brand=profile
                )
                
                if not admins_to_keep.exists():
                    return Responses.error_response(
                        status=status.HTTP_404_NOT_FOUND,
                        message='No valid admins found to keep',
                        data={}
                    )

                # Get the count of admins being kept
                admins_to_keep_count = admins_to_keep.count()
                
                # Delete all other admins except the ones to keep
                BrandAdmin.objects.filter(
                    brand=profile
                ).exclude(
                    id__in=admin_ids
                ).delete()
                
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message=f'Successfully kept {admins_to_keep_count} admin(s) and removed others',
                    data=BrandAdminSerializer(admins_to_keep, many=True).data
                )
            except Exception as e:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Error processing admin keep request',
                    data={'error': str(e)}
                )

        # Regular admin update logic
        admins_data = request.data.get('admins', [])
        saved_admins = []
        
        # Process all updates and creations
        for admin_data in admins_data:
            admin_id = admin_data.get('id')
            
            if admin_id:
                try:
                    admin = BrandAdmin.objects.get(id=admin_id)
                    # Verify this admin belongs to the current brand
                    if admin.brand_id != profile.id:
                        return Responses.error_response(
                            status=status.HTTP_404_NOT_FOUND,
                            message=f'Admin with id {admin_id} not found for this brand',
                            data={}
                        )
                    serializer = BrandAdminSerializer(
                        admin, 
                        data=admin_data, 
                        partial=True,
                        context={'request': request, 'brand_profile': profile}
                    )
                except BrandAdmin.DoesNotExist:
                    return Responses.error_response(
                        status=status.HTTP_404_NOT_FOUND,
                        message=f'Admin with id {admin_id} not found',
                        data={}
                    )
            else:
                # Create new admin
                serializer = BrandAdminSerializer(
                    data=admin_data,
                    context={'request': request, 'brand_profile': profile}
                )
            
            if serializer.is_valid():
                admin = serializer.save(brand=profile)
                saved_admins.append(serializer.data)
            else:
                return Responses.handle_validation_error(serializer.errors)
            
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Step 2 completed successfully',
            data=saved_admins
        )
        
    def handle_step_3(self, request, profile):
        # Step 3: Projects (same as TalentProfileView)
        if not profile:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Please complete step 1 first',
                data={}
            )
            
        # Handle project deletion
        project_to_delete = request.data.get('delete_project_id')
        if project_to_delete:
            try:
                project = Projects.objects.get(id=project_to_delete, user=request.user)
                project.delete()
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Project deleted successfully',
                    data={}
                )
            except Projects.DoesNotExist:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message=f'Project with id {project_to_delete} not found',
                    data={}
                )
            
        # Reuse the same project handling logic from TalentProfileView
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 3 skipped',
                data={}
            )
            
        projects_data = request.data.get('projects', [])
        if not isinstance(projects_data, list):
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Projects must be an array',
                data={}
            )
            
        saved_projects = []
        
        for project_data in projects_data:
            project_id = project_data.get('id')
            
            if project_id:
                # Update existing project
                try:
                    project = Projects.objects.get(id=project_id, user=request.user)
                    serializer = ProjectSerializer(
                        project, 
                        data=project_data, 
                        partial=True,
                        context={'request': request}
                    )
                except Projects.DoesNotExist:
                    return Responses.error_response(
                        status=status.HTTP_404_NOT_FOUND,
                        message=f'Project with id {project_id} not found',
                        data={}
                    )
            else:
                # Create new project
                serializer = ProjectSerializer(
                    data=project_data,
                    context={'request': request}
                )
                
            if serializer.is_valid():
                project = serializer.save()
                saved_projects.append(serializer.data)
            else:
                return Responses.handle_validation_error(serializer.errors)
                
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Step 3 completed successfully',
            data=saved_projects
        )
        
    def handle_step_4(self, request, profile):
        # Step 4: Social connects (same as TalentProfileView)
        if not profile:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Please complete step 1 first',
                data={}
            )
            
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 4 skipped',
                data={}
            )
            
        serializer = SocialConnectSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 4 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)
        
    def handle_step_5(self, request, profile):
        # Step 5: Bank account details (same as TalentProfileView)
        if not profile:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Please complete step 1 first',
                data={}
            )
            
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 5 skipped',
                data={}
            )
            
        try:
            bank_account = BankAccountDetails.objects.get(user=request.user)
            serializer = BankAccountDetailsSerializer(
                bank_account, 
                data=request.data, 
                partial=True, 
                context={'request': request}
            )
        except BankAccountDetails.DoesNotExist:
            serializer = BankAccountDetailsSerializer(
                data=request.data, 
                context={'request': request}
            )
            
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 5 completed successfully',
                data=serializer.data
            )
        return Responses.handle_validation_error(serializer.errors)
        

class SocialMediaAnalyticsView(APIView):
    """View to fetch and manage social media analytics"""
    permission_classes = [IsAuthenticated]

    def get(self, request, platform_id=None):
        """Get analytics for all or specific platform"""
        try:
            if platform_id:
                # Get analytics for specific platform
                analytics = SocialMediaAnalytics.objects.filter(
                    social_connect__user=request.user,
                    social_connect__platform_id=platform_id
                ).select_related('social_connect__platform').prefetch_related('posts').first()
                
                if not analytics:
                    return Responses.error_response(
                        status=status.HTTP_404_NOT_FOUND,
                        message='No analytics found for this platform',
                        data={}
                    )
                
                serializer = SocialMediaAnalyticsSerializer(analytics)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Analytics retrieved successfully',
                    data=serializer.data
                )
            else:
                # Get analytics for all platforms
                analytics = SocialMediaAnalytics.objects.filter(
                    social_connect__user=request.user
                ).select_related('social_connect__platform').prefetch_related('posts')
                
                serializer = SocialMediaAnalyticsSerializer(analytics, many=True)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Analytics retrieved successfully',
                    data=serializer.data
                )
                
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to retrieve analytics',
                data={'error': str(e)}
            )

    def post(self, request):
        """Fetch fresh analytics for a platform"""
        try:
            platform_id = request.data.get('platform')
            if not platform_id:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Platform ID is required',
                    data={}
                )

            # Get social connect
            social_connect = SocialConnects.objects.filter(
                user=request.user,
                platform_id=platform_id,
                connected=True
            ).first()

            if not social_connect:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message='No connected account found for this platform',
                    data={}
                )

            # Fetch fresh analytics
            analytics_data = AnalyticsService.fetch_analytics(social_connect)
            
            # Get the latest analytics record
            analytics = SocialMediaAnalytics.objects.filter(
                social_connect=social_connect
            ).select_related('social_connect__platform').prefetch_related('posts').first()
            
            serializer = SocialMediaAnalyticsSerializer(analytics)
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Analytics fetched successfully',
                data=serializer.data
            )

        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to fetch analytics',
                data={'error': str(e)}
            )
class UpdateContactView(APIView):
    """
    API View to update user's phone number and/or email
    """
    permission_classes = [IsAuthenticated]

    def patch(self, request):
        serializer = UpdateContactSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Contact details updated successfully',
                data=serializer.data
            )
        return Responses.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message='Validation error',
            data=serializer.errors
        )

class SocialMediaConnectView(APIView):
    """View to handle social media connections"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Connect a social media account"""
        try:
            platform_id = request.data.get('platform')
            url = request.data.get('url')
            
            if not platform_id or not url:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Platform ID and URL are required',
                    data={}
                )

            platform = SocialMediaPlatform.objects.get(id=platform_id)
            
            # Save or update social connection
            social_connect = SocialConnects.objects.filter(
                user=request.user,
                platform=platform
            ).first()

            if not social_connect:
                social_connect = SocialConnects(
                    user=request.user,
                    platform=platform
                )

            # Extract platform_user_id from URL based on platform
            if platform.name.lower() == 'youtube':
                # Handle YouTube URLs
                if 'youtube.com/channel/' in url:
                    platform_user_id = url.split('youtube.com/channel/')[-1].split('?')[0].split('/')[0]
                elif 'youtube.com/user/' in url:
                    platform_user_id = url.split('youtube.com/user/')[-1].split('?')[0].split('/')[0]
                elif 'youtube.com/@' in url:
                    platform_user_id = url.split('youtube.com/@')[-1].split('?')[0].split('/')[0]
                elif 'youtube.com/c/' in url:
                    platform_user_id = url.split('youtube.com/c/')[-1].split('?')[0].split('/')[0]
                else:
                    # Try to extract username from URL or use as is
                    platform_user_id = url.split('youtube.com/')[-1].split('?')[0].split('/')[0]
                    if not platform_user_id:
                        platform_user_id = url  # Use the input as is if it might be a username
            elif platform.name.lower() == 'instagram':
                # Handle Instagram URLs
                if 'instagram.com/' in url:
                    platform_user_id = url.split('instagram.com/')[-1].split('?')[0].strip('/')
                else:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Invalid Instagram URL format',
                        data={}
                    )
            else:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message=f'Platform {platform.name} is not supported yet',
                    data={}
                )

            social_connect.platform_user_id = platform_user_id
            social_connect.url = url
            social_connect.connected = True
            social_connect.save()

            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Social media account connected successfully',
                data={
                    'platform': platform.name,
                    'url': url,
                    'platform_user_id': platform_user_id
                }
            )

        except SocialMediaPlatform.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Platform not found',
                data={}
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to connect social media account',
                data={'error': str(e)}
            )

    def delete(self, request, platform_id):
        """Disconnect a social media account"""
        try:
            platform = SocialMediaPlatform.objects.get(id=platform_id)
            social_connect = SocialConnects.objects.filter(
                user=request.user,
                platform=platform
            ).first()

            if not social_connect:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message='No connected account found for this platform',
                    data={}
                )

            social_connect.connected = False
            social_connect.save()

            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Social media account disconnected successfully',
                data={}
            )

        except SocialMediaPlatform.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Platform not found',
                data={}
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to disconnect social media account',
                data={'error': str(e)}
            )

class GenerateAadharOTPView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        aadhaar_number = request.data.get("aadhaar_number")

        if not aadhaar_number:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Aadhaar number is required."
            )

        try:
            response_data = generate_aadhar_otp(aadhaar_number)

            if response_data.get("success"):
                # Successfully received response from Surepass
                data = response_data.get("data", {})
                client_id = data.get("data", {}).get("client_id")

                if client_id:
                    AadhaarClient.objects.update_or_create(
                        user=user,
                        defaults={"client_id": client_id}
                    )

                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message="OTP successfully sent to your registered Aadhaar number.",
                    data={'client_id': client_id}
                )

            # If timeout
            if response_data.get("type") == "timeout":
                return Responses.error_response(
                    status=status.HTTP_504_GATEWAY_TIMEOUT,
                    message=response_data.get("message")
                )

            # If 429 from Surepass
            if response_data.get("status_code") == 429:
                return Responses.error_response(
                    status=status.HTTP_429_TOO_MANY_REQUESTS,
                    message="You have reached the maximum number of OTP requests. Please wait and try again later."
                )

            # All other Surepass errors
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message=response_data.get("message", "Failed to generate OTP.")
            )

        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message=f"Error while generating Aadhaar OTP: {str(e)}"
            )

        

class AadhaarOTPVerifyAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        client_id = request.data.get("client_id")
        otp = request.data.get("otp")

        if not client_id or not otp:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Client ID and OTP are required."
            )

        try:
            # Check if AadhaarClient exists for user and client_id
            aadhaar_client = AadhaarClient.objects.filter(user=user, client_id=client_id).first()
            if not aadhaar_client:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message="No Aadhaar verification session found for this client ID and user."
                )

            # Call verification API
            result = verify_aadhar_otp(client_id, otp)

            if result.get("success"):
                data = result.get("data", {})

                # Update AadhaarClient fields
                aadhaar_client.full_name = data.get("full_name") or aadhaar_client.full_name
                dob_str = data.get("dob")
                if dob_str:
                    from datetime import datetime
                    try:
                        aadhaar_client.dob = datetime.strptime(dob_str, "%Y-%m-%d").date()
                    except ValueError:
                        pass
                aadhaar_client.gender = data.get("gender") or aadhaar_client.gender
                aadhaar_client.aadhaar_number = data.get("aadhaar_number")
                aadhaar_client.is_aadhar_verified = True
                aadhaar_client.save()

                # Update user's KYC status if needed
                update_kyc_status(user)

                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message=result.get("message", "OTP verified successfully."),
                    data={"is_aadhar_verified": True}
                )

            else:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message=result.get("message", "OTP verification failed."),
                    data=result
                )

        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message=f"Error while verifying Aadhaar OTP: {str(e)}"
            )


class VerifyPANAPIView(APIView):

    def post(self, request):
        pan_number = request.data.get("pan_number")
        user = request.user

        if not pan_number:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="PAN number is required.",
                data={"pan_number": ["This field is required."]}
            )

        result = verify_pan(pan_number)

        if not result.get("success") or "data" not in result:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message=result.get("message", "PAN verification failed."),
                data=result.get("raw_response")
            )

        pan_data = result["data"]
        full_name = pan_data.get("full_name", "").strip().lower()

        # Handle Talent User Verification
        if user.user_type == UserTypeChoices.TALENT.value[0]: 
            try:
                talent_profile = TalentProfile.objects.get(user=user)
                talent_profile_name = talent_profile.name.strip().lower() if talent_profile.name else ""

                if full_name == talent_profile_name:
        
                    PANVerification.objects.update_or_create(
                        user=user,
                        defaults={
                            "pan_number": pan_data.get("pan_number"),
                            "client_id": pan_data.get("client_id"),
                            "full_name": pan_data.get("full_name"),
                            "full_name_split": pan_data.get("full_name_split"),
                            "masked_aadhaar": pan_data.get("masked_aadhaar"),
                            "email": pan_data.get("email"),
                            "phone_number": pan_data.get("phone_number"),
                            "gender": pan_data.get("gender"),
                            "dob": pan_data.get("dob"),
                            "is_pan_verified": True
                        }
                    )

                    update_kyc_status(user)
                    return Responses.success_response(
                        status=status.HTTP_200_OK,
                        message="PAN verification successful.",
                        data={"is_pan_verified": True}
                    )
                else:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message="PAN data received, but verification failed due to a mismatch between the first and last name in your Talent Trade account.",
                        data={"is_pan_verified": False}
                    )
                
            except TalentProfile.DoesNotExist:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message="Talent profile not found for this user.",
                )
            
        # Handle Brand User Verification
        elif user.user_type == UserTypeChoices.BRAND.value[0]:
            try:
                brand_profile = BrandProfile.objects.get(user=user)
                brand_admin_name = brand_profile.brand_admin_name.strip().lower() if brand_profile.brand_admin_name else ""

                if full_name == brand_admin_name:
                    PANVerification.objects.update_or_create(
                        user=user,
                        defaults={
                            "pan_number": pan_data.get("pan_number"),
                            "client_id": pan_data.get("client_id"),
                            "full_name": pan_data.get("full_name"),
                            "full_name_split": pan_data.get("full_name_split"),
                            "masked_aadhaar": pan_data.get("masked_aadhaar"),
                            "email": pan_data.get("email"),
                            "phone_number": pan_data.get("phone_number"),
                            "gender": pan_data.get("gender"),
                            "dob": pan_data.get("dob"),
                            "is_pan_verified": True
                        }
                    )

                    update_kyc_status(user)
                    return Responses.success_response(
                        status=status.HTTP_200_OK,
                        message="PAN verification successful.",
                        data={"is_pan_verified": True}
                    )
                else:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message="PAN data received, but verification failed due to a mismatch between the PAN full name and your brand admin name.",
                        data={"is_pan_verified": False}
                    )
            
            except BrandProfile.DoesNotExist:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message="Brand profile not found for this user.",
                )
        
        else:
            return Responses.error_response(
                status=status.HTTP_403_FORBIDDEN,
                message="PAN verification is not applicable for this user type.",
            )




class FCMTokenViewSet(ModelViewSet):
    """
    ViewSet to handle FCM token (registration_id) updates based on device_id
    """
    queryset = Token.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Restrict queryset to tokens for the authenticated user
        return Token.objects.filter(user=self.request.user)

    def create(self, request):
        user = request.user
        device_id = request.data.get("device_id")
        registration_id = request.data.get("registration_id")

        if not registration_id:
            return Responses.handle_validation_error({"registration_id": ["This field is required."]})
        if not device_id:
            return Responses.handle_validation_error({"device_id": ["This field is required."]})

        token_obj, created = Token.objects.update_or_create(
            user=user,
            device_id=device_id,
            defaults={
                "registration_id": registration_id
            }
        )

        return Responses.success_response(
            status=status.HTTP_200_OK if not created else status.HTTP_201_CREATED,
            message="FCM token updated." if not created else "FCM token registered.",
            data={"device_id": device_id, "registration_id": registration_id}
        )
                
            
class IndustryTypesView(APIView):
    """View to get all active industry types"""
    permission_classes = [AllowAny]  # Allow anyone to access this endpoint

    def get(self, request):
        industry_types = IndustryType.objects.filter(is_active=True).order_by('name')
        serializer = IndustryTypeSerializer(industry_types, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Industry types retrieved successfully',
            data=serializer.data
        )
    

class NotificationListView(ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = "id"

    def get_queryset(self):
        # You can add filters if you want only unread notifications etc.
        return Notification.objects.filter(user=self.request.user)
    
    def patch(self, request, *args, **kwargs):
        """
        PATCH: Mark a single notification or all notifications as read.
        """
        if kwargs.get("id"):
            updated = self.filter_queryset(self.get_queryset()).filter(
                id=kwargs["id"]
            ).update(is_read=True)
            if updated:
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message="Notification marked as read successfully."
                )
            else:
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message="Notification not found."
                )

        # Mark all unread notifications as read
        self.filter_queryset(self.get_queryset()).filter(is_read=False).update(is_read=True)

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="All unread notifications marked as read successfully."
        )
    

class BrandNameListView(ModelViewSet):
    """
    API to list all brand names (without pagination)
    """
    queryset = BrandProfile.objects.all().order_by('brand_name')
    serializer_class = BrandNameSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(brand_name__icontains=search)
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Brand names fetched successfully",
            data=serializer.data
        )



class TalentListViewSet(ModelViewSet):
    permission_classes = [IsAuthenticated]
    lookup_field = "id"
    search_fields = [
            'talent_profile__name',
            'email',
            'projects__category_tags__name',
            'projects__social_tags__name',
            'projects__description',
        ]    
    filterset_class = TalentFilter
    pagination_class = CustomPagination  

    def get_queryset(self):
        return (
            User.objects
            .filter(user_type=UserTypeChoices.TALENT.value[0])
            .select_related('talent_profile')
            .prefetch_related(
                'talent_profile__expertise_areas',
                'talent_profile__languages',
                'projects__social_tags',
                'projects__category_tags',
                'projects__user__social_rate_cards__platform_category',
            )
            .distinct()
        )

    def get_serializer_class(self):
        if self.action == "retrieve":
            return TalentDetailsSerializer
        return TalentListSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.get_paginated_response(serializer.data).data
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message="Talent list fetched successfully.",
                data=paginated_data
            )

        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Talent list fetched successfully.",
            data=serializer.data
        )
    

class TalentBookmarkViewSet(ModelViewSet):
    """
    ViewSet to handle bookmarking and unbookmarking of talents by brands
    """
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    filterset_class = TalentFilter

    def get_queryset(self):
        return User.objects.filter(
            bookmarked_by_brands__brand=self.request.user
        ).distinct()

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())  

        paginator = self.pagination_class()
        paginated_qs = paginator.paginate_queryset(queryset, request)

        serializer = TalentListSerializer(paginated_qs, many=True, context={'request': request})
        return paginator.get_paginated_response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        talent_id = self.kwargs.get('talent_id')
        brand = request.user

        if not talent_id:
            raise ValidationError({"talent_id": "Talent ID is required."})

        try:
            talent = User.objects.get(id=talent_id)
        except User.DoesNotExist:
            raise NotFound("Talent not found.")

        bookmark, created = BookmarkTalent.objects.get_or_create(
            brand=brand, talent=talent
        )

        if created:
            return Responses.success_response(
                status=status.HTTP_201_CREATED,
                message="Talent bookmarked successfully."
            )
        else:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message="Talent already bookmarked."
            )

    def destroy(self, request, *args, **kwargs):
        talent_id = self.kwargs.get('talent_id')
        brand = request.user

        try:
            talent = User.objects.get(id=talent_id)
        except User.DoesNotExist:
            raise NotFound("Talent not found.")

        deleted, _ = BookmarkTalent.objects.filter(
            brand=brand, talent=talent
        ).delete()

        if deleted:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message="Bookmark removed successfully."
            )
        else:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Bookmark does not exist."
            )


class DashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        is_unread_notification = Notification.objects.filter(user=user, is_read=False).exists()

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Dashboard data fetched successfully.",
            data={
                "is_unread_notification": is_unread_notification
            }
        )

class ContactValidationView(APIView):
    """
    API View for validating email or mobile number
    Checks format and availability
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = ContactValidationSerializer(data=request.data)
        if not serializer.is_valid():
            return Responses.handle_validation_error(serializer.errors)
        
        email = serializer.validated_data.get('email')
        mobile_number = serializer.validated_data.get('mobile_number')
        
        validation_result = {
            'is_valid': True,
            'is_available': True,
            'message': 'Contact information is valid and available',
            'suggestions': []
        }
        
        try:
            if email:
                # Check email availability
                user_exists = User.objects.filter(email=email).exists()
                
                if user_exists:
                    existing_user = User.objects.get(email=email)
                    validation_result.update({
                        'is_available': False,
                        'message': 'Email already exists in our system',
                        'existing_user_type': existing_user.user_type
                    })
                else:
                    validation_result.update({
                        'is_available': True,
                        'message': 'Email is available'
                    })
                
                # Add email-specific suggestions
                if not user_exists:
                    validation_result['suggestions'].append('Email is available for use')
                else:
                    validation_result['suggestions'].append('Email is already registered')
                
            elif mobile_number:
                # Check mobile number availability
                user_exists = User.objects.filter(mobile_number=mobile_number).exists()
                
                if user_exists:
                    existing_user = User.objects.get(mobile_number=mobile_number)
                    validation_result.update({
                        'is_available': False,
                        'message': 'Mobile number already exists in our system',
                        'existing_user_type': existing_user.user_type
                    })
                else:
                    validation_result.update({
                        'is_available': True,
                        'message': 'Mobile number is available'
                    })
                
                # Add mobile-specific suggestions
                if not user_exists:
                    validation_result['suggestions'].append('Mobile number is available for use')
                else:
                    validation_result['suggestions'].append('Mobile number is already registered')
            
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Contact validation completed',
                data=validation_result
            )
            
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message=f'Error during validation: {str(e)}',
                data={}
            )


class UserRolesAPIView(APIView):
    """
    API view to get user roles and permissions
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get current user's roles and permissions
        """
        try:
            user = request.user

            # Get user's active roles
            active_roles = user.get_active_roles()

            # Get role permissions
            role_permissions = user.get_role_permissions()

            # Format roles data
            roles_data = []
            for role in active_roles:
                roles_data.append({
                    'id': role.id,
                    'name': role.name,
                    'description': role.description,
                    'permission_count': role.permission_count
                })

            # Format permissions data
            permissions_data = []
            for perm in role_permissions:
                permissions_data.append({
                    'id': perm.id,
                    'name': perm.name,
                    'codename': perm.codename,
                    'app_label': perm.content_type.app_label
                })

            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='User roles and permissions retrieved successfully',
                data={
                    'user_id': user.id,
                    'email': user.email,
                    'is_superuser': user.is_superuser,
                    'is_staff': user.is_staff,
                    'roles': roles_data,
                    'role_permissions': permissions_data,
                    'total_roles': len(roles_data),
                    'total_permissions': len(permissions_data)
                }
            )

        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message='Failed to retrieve user roles and permissions',
                data={'error': str(e)}
            )