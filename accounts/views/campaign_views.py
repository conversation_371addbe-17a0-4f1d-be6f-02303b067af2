from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils import timezone
from accounts.services.campaign_service import CampaignService
from accounts.models import Campaign
from talent_trade.utils.responses import Responses
from datetime import datetime

class CampaignView(APIView):
    """View to handle campaign creation and management"""
    permission_classes = [IsAuthenticated]

    def get(self, request, campaign_id=None):
        """Get campaign details"""
        try:
            if campaign_id:
                # Get specific campaign
                campaign = Campaign.objects.get(id=campaign_id, brand=request.user)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Campaign retrieved successfully',
                    data=self._get_campaign_data(campaign)
                )
            else:
                # List all campaigns for the brand
                campaigns = Campaign.objects.filter(brand=request.user)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Campaigns retrieved successfully',
                    data=[self._get_campaign_data(campaign) for campaign in campaigns]
                )
        except Campaign.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Campaign not found',
                data={}
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to retrieve campaign(s)',
                data={'error': str(e)}
            )

    def post(self, request):
        """Handle campaign creation steps"""
        try:
            step = request.data.get('step')
            campaign_id = request.data.get('campaign_id')
            
            if not step:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Step number is required',
                    data={}
                )

            # For steps 2-5, campaign_id is required
            if step > 1 and not campaign_id:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Campaign ID is required for this step',
                    data={}
                )

            # Handle each step
            if step == 1:
                # Validate required fields
                required_fields = ['campaign_name', 'final_submission_date']
                for field in required_fields:
                    if field not in request.data:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message=f'{field} is required',
                            data={}
                        )
                
                # Parse and validate date
                try:
                    submission_date = datetime.strptime(request.data['final_submission_date'], '%Y-%m-%d').date()
                    if submission_date < timezone.now().date():
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message='Final submission date cannot be in the past',
                            data={}
                        )
                except ValueError:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Invalid date format. Use YYYY-MM-DD',
                        data={}
                    )

                campaign = CampaignService.handle_step_1(request.user.id, request.data)
                
            else:
                # Get campaign for steps 2-5
                try:
                    campaign = Campaign.objects.get(id=campaign_id, brand=request.user)
                except Campaign.DoesNotExist:
                    return Responses.error_response(
                        status=status.HTTP_404_NOT_FOUND,
                        message='Campaign not found',
                        data={}
                    )

                if step == 2:
                    language_ids = request.data.get('language_ids', [])
                    campaign = CampaignService.handle_step_2(campaign, language_ids)
                    
                elif step == 3:
                    # Validate budget data
                    budget_data = request.data
                    if 'min_budget' in budget_data and 'max_budget' in budget_data:
                        min_budget = float(budget_data['min_budget'])
                        max_budget = float(budget_data['max_budget'])
                        if min_budget > max_budget:
                            return Responses.error_response(
                                status=status.HTTP_400_BAD_REQUEST,
                                message='Minimum budget cannot be greater than maximum budget',
                                data={}
                            )
                    campaign = CampaignService.handle_step_3(campaign, budget_data)
                    
                elif step == 4:
                    deliverables_data = request.data.get('deliverables', [])
                    if not deliverables_data:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message='At least one deliverable is required',
                            data={}
                        )
                    created_deliverables = CampaignService.handle_step_4(campaign, deliverables_data)
                    
                elif step == 5:
                    go_live_dates_data = request.data.get('go_live_dates', [])
                    if not go_live_dates_data:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message='Go live dates are required',
                            data={}
                        )
                    created_dates = CampaignService.handle_step_5(campaign, go_live_dates_data)
                    
                else:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Invalid step number',
                        data={}
                    )

            return Responses.success_response(
                status=status.HTTP_200_OK,
                message=f'Step {step} completed successfully',
                data=self._get_campaign_data(campaign)
            )

        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message=f'Failed to process step {step}',
                data={'error': str(e)}
            )

    def _get_campaign_data(self, campaign):
        """Helper method to format campaign data for response"""
        return {
            'id': campaign.id,
            'campaign_name': campaign.campaign_name,
            'brief': campaign.brief,
            'hashtags': campaign.hashtags,
            'final_submission_date': campaign.final_submission_date,
            'preferred_state': campaign.preferred_state,
            'preferred_city': campaign.preferred_city,
            'languages': [{'id': lang.id, 'name': lang.name} for lang in campaign.languages.all()],
            'min_budget': campaign.min_budget,
            'max_budget': campaign.max_budget,
            'status': campaign.status,
            'platform_selections': [
                {
                    'platform': {
                        'id': ps.platform.id,
                        'name': ps.platform.name
                    },
                    'deliverables': [
                        {
                            'id': d.id,
                            'category_type': {
                                'id': d.category_type.id,
                                'name': d.category_type.name
                            },
                            'count': d.count,
                            'go_live_dates': [
                                {
                                    'id': gld.id,
                                    'date': gld.go_live_date,
                                    'is_active': gld.is_active
                                }
                                for gld in d.go_live_dates.all()
                            ]
                        }
                        for d in ps.deliverables.all()
                    ]
                }
                for ps in campaign.platform_selections.all()
            ]
        } 