from django.contrib.auth.models import AbstractUser
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator, URLValidator
from django.db import models
from rest_framework.authtoken.models import Token as DjangoToken


# from talent_trade.services.push_notification_service import NotificationHelper
from talent_trade.services.push_notification_service import NotificationHelper
from talent_trade.utils.choices import KYCStatusChoices, NotificationModuleChoice
from .choices import UserTypeChoices,GenderChoices
from django.utils import timezone
from datetime import timedelta
from django.core.exceptions import ValidationError
import re
from django.contrib.postgres.fields import ArrayField
from django.db.models import J<PERSON><PERSON>ield
from django.contrib.auth.base_user import BaseUserManager
from django.conf import settings


class BaseModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class User(AbstractUser):
    user_type = models.CharField(
        max_length=20,
        choices=UserTypeChoices.get_choices()
    )
    # Add is_admin_user field
    is_admin_user = models.BooleanField(default=False)
    mobile_number = models.CharField(
        max_length=10,
        validators=[RegexValidator(regex=r'^\d{10}$', message='Enter a valid 10-digit mobile number.')],
        unique=True,
    )
    country_code = models.CharField(
        max_length=5,
        default='+91'
    )
    email = models.EmailField(unique=True)
    is_email_verified = models.BooleanField(default=False)
    is_mobile_verified = models.BooleanField(default=False)
    username = models.CharField(max_length=100, null=True, blank=True)
    kyc_status = models.IntegerField(
            choices=KYCStatusChoices.choices,
            default=KYCStatusChoices.NOT_STARTED
        )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def save(self, *args, **kwargs):
        if self.email:
            self.email = self.email.lower()
        super().save(*args, **kwargs)

    def get_role_permissions(self):
        """Get all permissions from assigned roles"""
        from django.contrib.auth.models import Permission
        role_permissions = Permission.objects.filter(
            role__role_users__user=self,
            role__role_users__is_active=True,
            role__is_active=True
        ).distinct()
        return role_permissions

    def has_role_permission(self, permission_codename, app_label=None):
        """Check if user has a specific permission through roles"""
        role_permissions = self.get_role_permissions()
        if app_label:
            return role_permissions.filter(
                codename=permission_codename,
                content_type__app_label=app_label
            ).exists()
        return role_permissions.filter(codename=permission_codename).exists()

    def get_active_roles(self):
        """Get all active roles assigned to this user"""
        return Role.objects.filter(
            role_users__user=self,
            role_users__is_active=True,
            is_active=True
        ).distinct()

    # def __str__(self):
    #     return self.username

class OTP(BaseModel):
    """
    Model to store OTP records for both email and mobile verification
    """
    email = models.EmailField(null=True, blank=True)
    phone = models.CharField(max_length=10, null=True, blank=True)
    otp = models.CharField(max_length=6)
    is_verified = models.BooleanField(default=False)
    is_used = models.BooleanField(default=False)
    expires_at = models.DateTimeField()

    class Meta:
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
            models.Index(fields=['created_at']),
        ]
        verbose_name = 'OTP'
        verbose_name_plural = 'OTPs'
        ordering = ['-created_at']

    def __str__(self):
        return f"OTP for {self.email or self.phone}"

    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Set OTP expiry to 10 minutes from creation
            self.expires_at = timezone.now() + timedelta(minutes=10)
        if self.email:
            self.email = self.email.lower()
        super().save(*args, **kwargs)

    def is_valid(self):
        """
        Check if OTP is valid (not used and not expired)
        """
        return not self.is_used and timezone.now() <= self.expires_at

    @property
    def is_expired(self):
        """
        Check if OTP has expired
        """
        return timezone.now() > self.expires_at

class Language(BaseModel):
    """
    Model to store supported languages
    """
    name = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)
    value = models.CharField(max_length=100,null=True,blank=True)

    class Meta:
        verbose_name = 'Language'
        verbose_name_plural = 'Languages'
        ordering = ['name']

    def __str__(self):
        return self.name

class ExpertiseArea(BaseModel):
    """
    Model to store expertise areas
    """
    name = models.CharField(max_length=100, unique=True)
    icon = models.CharField(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    type = models.CharField(max_length=255, null=True, blank=True)


    class Meta:
        verbose_name = 'Expertise Area'
        verbose_name_plural = 'Expertise Areas'
        ordering = ['name']

    def __str__(self):
        return self.name
    
class SocialMediaPlatform(models.Model):
    """
    Represents a social media platform like Instagram, YouTube, etc.
    """
    name = models.CharField(max_length=50, unique=True)
    icon = models.ImageField(upload_to='platform_icons/', blank=True, null=True)
    
    def __str__(self):
        return self.name

class PlatformCategory(models.Model):
    """
    Categories for each platform (e.g., Story, Post, Reel for Instagram).
    """
    platform = models.ForeignKey(SocialMediaPlatform, on_delete=models.CASCADE, related_name='platforms')
    name = models.CharField(max_length=50)
    
    def __str__(self):
        return self.name

class TalentProfile(BaseModel):
    """
    Represents a talent's profile with all their professional information.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='talent_profile')
    profile_pic = models.ImageField(upload_to='profile_pics/', blank=True, null=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    gender = models.CharField(max_length=1, choices=GenderChoices.get_choices(), null=True, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    bio = models.TextField(max_length=2000, null=True, blank=True, help_text="Maximum 2000 characters")
    state = models.CharField(max_length=100, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    is_gst = models.BooleanField(default=False)
    gst_number = models.CharField(
        max_length=15,
        null=True,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}$',
                message='Enter a valid GST number'
            )
        ]
    )
    expertise_areas = models.ManyToManyField(ExpertiseArea, related_name='talent_profiles', blank=True)
    languages = models.ManyToManyField(Language, related_name='talent_profiles', blank=True)

    class Meta:
        verbose_name = 'Talent Profile'
        verbose_name_plural = 'Talent Profiles'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['name']),
            models.Index(fields=['state', 'city']),
            models.Index(fields=['is_gst']),
            models.Index(fields=['gst_number']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.name or 'Unnamed'} - {self.user.mobile_number}"

    def clean(self):
        if self.is_gst and not self.gst_number:
            raise ValidationError({'gst_number': 'GST number is required when GST is enabled'})
        if self.date_of_birth and self.date_of_birth > timezone.now().date():
            raise ValidationError({'date_of_birth': 'Date of birth cannot be in the future'})

class Projects(BaseModel):
    """
    Represents a project or work sample in a talent's portfolio.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='projects')
    title = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(max_length=2000, null=True, blank=True, help_text="Maximum 2000 characters")
    social_tags = models.ManyToManyField(SocialMediaPlatform, related_name='projects', blank=True)
    category_tags = models.ManyToManyField(ExpertiseArea, related_name='projects', blank=True)
    image  = models.ImageField(upload_to="project_docs/images/",null=True,blank=True)
    video = models.FileField(upload_to="project_docs/videos/",null=True,blank=True)
    thumbnail = models.ImageField(upload_to="project_docs/thumbnails/",null=True,blank=True)

    class Meta:
        verbose_name = 'Project'
        verbose_name_plural = 'Projects'
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['title']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.title or 'Untitled Project'} - {self.user.email}"

    # def clean(self):
    #     if self.social_tags and not isinstance(self.social_tags, list):
    #         raise ValidationError({'social_tags': 'Social tags must be a list'})
    #     if self.category_tags and not isinstance(self.category_tags, list):
    #         raise ValidationError({'category_tags': 'Category tags must be a list'})

class SocialConnects(BaseModel):
    """
    Represents a talent's social media profile links.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='social_connects')
    platform = models.ForeignKey(SocialMediaPlatform, on_delete=models.CASCADE, related_name='social_connects')
    url = models.URLField(max_length=500, null=True, blank=True)
    connected = models.BooleanField(default=False)
    access_token = models.TextField(null=True, blank=True)
    refresh_token = models.TextField(null=True, blank=True)
    platform_user_id = models.CharField(max_length=255, null=True, blank=True)
    token_expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'Social Connect'
        verbose_name_plural = 'Social Connects'
        unique_together = ['user', 'platform']
        indexes = [
            models.Index(fields=['user', 'platform']),
            models.Index(fields=['connected']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.platform.name}"

class SocialMediaCategoryRate(models.Model):
    """
    Stores the min and max price range for each platform category.
    """
    platform_category = models.ForeignKey(PlatformCategory, on_delete=models.CASCADE, related_name='rates')
    min_price = models.DecimalField(max_digits=10, decimal_places=2)
    max_price = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = 'Social Media Rate Card'
        verbose_name_plural = 'Social Media Rate Cards'
        indexes = [
            models.Index(fields=['min_price', 'max_price']),
        ]

    def __str__(self):
        return f"{self.platform_category.name} - {self.min_price} - {self.max_price}"

class BankAccountDetails(BaseModel):
    """
    Represents a talent's bank account and financial information.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='bank_account_details')
    account_number = models.CharField(
        max_length=20,
        validators=[RegexValidator(regex=r'^\d+$', message='Account number must contain only digits')],
        null=True,
        blank=True
    )
    ifsc_code = models.CharField(
        max_length=11,
        validators=[RegexValidator(regex=r'^[A-Z]{4}0[A-Z0-9]{6}$', message='Please enter a valid IFSC code')],
        null=True,
        blank=True
    )
    account_holder_name = models.CharField(max_length=100, null=True, blank=True)
    
    aadhar_number = models.CharField(
        max_length=12,
        validators=[RegexValidator(regex=r'^\d{12}$', message='Please enter a valid 12-digit Aadhar number')],
        null=True,
        blank=True
    )
    pan_number = models.CharField(
        max_length=10,
        validators=[RegexValidator(regex=r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', message='Please enter a valid PAN number')],
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = 'Bank Account Detail'
        verbose_name_plural = 'Bank Account Details'
        indexes = [
            models.Index(fields=['user', 'account_number']),
            models.Index(fields=['aadhar_number']),
            models.Index(fields=['pan_number']),
            models.Index(fields=['ifsc_code']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.account_holder_name} - {self.account_number[-4:]}"

class SocialRateCard(models.Model):
    """
    Stores the min and max price range for each platform category.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='social_rate_cards',null=True, blank=True)
    platform_category = models.ForeignKey(PlatformCategory, on_delete=models.CASCADE, related_name='social_rate_cards')
    min_price = models.DecimalField(max_digits=10, decimal_places=2)
    max_price = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = 'Social Rate Card'
        verbose_name_plural = 'Social Rate Cards'
        unique_together = ['user', 'platform_category']
        indexes = [
            models.Index(fields=['user', 'platform_category']),
            models.Index(fields=['min_price', 'max_price']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.platform_category}"

    def clean(self):
        if self.max_price < self.min_price:
            raise ValidationError({'max_price': 'Maximum price cannot be less than minimum price'})

class BrandProfile(BaseModel):
    """
    Represents a brand's profile with all their professional information.
    """
    INDUSTRY_CHOICES = [
        ('industry1', 'Industry 1'),
        ('industry2', 'Industry 2'),
        ('industry3', 'Industry 3'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='brand_profile')
    brand_logo = models.ImageField(upload_to='brand_logos/', blank=True, null=True)
    brand_name = models.CharField(max_length=100)  # only mandatory field
    brand_admin_name = models.CharField(max_length=100, null=True, blank=True)
    industry_type = models.CharField(max_length=100, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    gst_number = models.CharField(
        max_length=15,
        null=True,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}$',
                message='Enter a valid GST number'
            )
        ]
    )
    company_name_on_cin = models.CharField(max_length=200, null=True, blank=True)
    cin_number = models.CharField(max_length=21, null=True, blank=True)
    cin_certificate = models.FileField(upload_to='cin_certificates/', blank=True, null=True)
    class Meta:
        verbose_name = 'Brand Profile'
        verbose_name_plural = 'Brand Profiles'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['brand_name']),
            models.Index(fields=['state', 'city']),
            models.Index(fields=['gst_number']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.brand_name

class IndustryType(BaseModel):
    """
    Represents different industry types for brands
    """
    name = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'Industry Type'
        verbose_name_plural = 'Industry Types'
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

class BrandAdmin(BaseModel):
    """
    Represents admins for a brand
    """
    brand = models.ForeignKey(BrandProfile, on_delete=models.CASCADE, related_name='admins')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='brand_admin_roles', null=True, blank=True)
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(
        max_length=10,
        validators=[RegexValidator(regex=r'^\d{10}$', message='Enter a valid 10-digit mobile number.')]
    )
    # Remove is_primary field or set default to False
    is_primary = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Brand Admin'
        verbose_name_plural = 'Brand Admins'
        unique_together = ('brand', 'email')
        indexes = [
            models.Index(fields=['brand']),
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
        ]

    def __str__(self):
        return f"{self.name} (Admin for {self.brand.brand_name})"

    def save(self, *args, **kwargs):
        if self.email:
            self.email = self.email.lower()
        super().save(*args, **kwargs)

class SocialMediaAnalytics(BaseModel):
    """
    Stores analytics data for social media connections
    """
    social_connect = models.ForeignKey(SocialConnects, on_delete=models.CASCADE, related_name='analytics')
    followers_count = models.IntegerField(default=0)
    following_count = models.IntegerField(default=0)
    posts_count = models.IntegerField(default=0)
    average_reach = models.IntegerField(default=0)
    average_engagement = models.FloatField(default=0)
    
    class Meta:
        verbose_name = 'Social Media Analytics'
        verbose_name_plural = 'Social Media Analytics'
        indexes = [
            models.Index(fields=['social_connect']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Analytics for {self.social_connect.user.email} - {self.social_connect.platform.name}"

class SocialMediaPost(BaseModel):
    """
    Stores individual post data from social media platforms
    """
    analytics = models.ForeignKey(SocialMediaAnalytics, on_delete=models.CASCADE, related_name='posts')
    post_id = models.CharField(max_length=255)
    post_url = models.URLField()
    post_type = models.CharField(max_length=50)  # image, video, carousel, etc.
    reach = models.IntegerField(default=0)
    engagement = models.IntegerField(default=0)
    likes = models.IntegerField(default=0)
    comments = models.IntegerField(default=0)
    shares = models.IntegerField(default=0)
    posted_at = models.DateTimeField()

    class Meta:
        verbose_name = 'Social Media Post'
        verbose_name_plural = 'Social Media Posts'
        indexes = [
            models.Index(fields=['analytics', '-posted_at']),
            models.Index(fields=['post_id']),
            models.Index(fields=['post_type']),
        ]

    def __str__(self):
        return f"{self.post_type} post from {self.analytics.social_connect.platform.name}"

class AadhaarClient(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="aadhar_verification")
    aadhaar_number = models.CharField(max_length=12,null=True,blank=True)
    client_id = models.CharField(max_length=255)
    
    full_name = models.CharField(max_length=255, blank=True, null=True)
    dob = models.DateField(blank=True, null=True)
    gender = models.CharField(max_length=10, blank=True, null=True)
    
    is_aadhar_verified = models.BooleanField(default=False)
    

    class Meta:
        unique_together = ('user', 'aadhaar_number')
        verbose_name = "Aadhaar Client"
        verbose_name_plural = "Aadhaar Clients"

    def __str__(self):
        return f"AadhaarClient(user={self.user.username}, aadhaar_number={self.aadhaar_number})"
    

class PANVerification(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="pan_verification")
    pan_number = models.CharField(max_length=20)
    client_id = models.CharField(max_length=100)
    full_name = models.CharField(max_length=100)
    full_name_split = models.JSONField(null=True, blank=True)
    masked_aadhaar = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    phone_number = models.CharField(max_length=15, null=True, blank=True)
    gender = models.CharField(max_length=10, null=True, blank=True)
    dob = models.CharField(max_length=20, null=True, blank=True)
    is_pan_verified = models.BooleanField(default=False)


class Notification(BaseModel):
    """notification module"""

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="notifications"
    )

    module = models.IntegerField(
        choices=NotificationModuleChoice.choices,
        null=True
    )

    title = models.IntegerField(
        choices=NotificationModuleChoice.choices,
        null=True,
        blank=True
    )

    is_read = models.BooleanField(default=False)
    message = models.CharField(max_length=255)
    extra_info = models.JSONField(default=dict)
    is_triggered = models.BooleanField(default=False)

    class Meta:
        db_table = "notifications"
        ordering = ("-created_at",)

    def push(self):
        helper = NotificationHelper(
            user=self.user,
            message=self.message,
            title=self.get_title_display() if self.title else "",
            module=str(self.module),
            extra_info=self.extra_info,
            created_at=self.created_at,
        )
        was_sent = helper.push()
        if was_sent and not self.is_triggered:
            self.is_triggered = True
            self.save(update_fields=['is_triggered'])

class TokenManager(BaseUserManager):
    """user queryset manager , fetching all select related queries"""

    def get_queryset(self):
        """get queryset"""
        return super().get_queryset().select_related("user")

class Token(DjangoToken):
    """token model to allow multi device authentication"""

    key = models.CharField(max_length=40, db_index=True, unique=True, primary_key=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="tokens",
        on_delete=models.CASCADE,
        verbose_name=("User"),
    )
    device_id = models.CharField(
        ("device_id"),
        max_length=64,
        db_index=True,
        unique=False,
        editable=False,
        null=True,
        blank=True,
    )
    device_name = models.CharField(max_length=255, null=True, blank=True)
    device_os = models.CharField(max_length=255, null=True, blank=True)
    registration_id = models.CharField(max_length=255, blank=True, null=True)

    objects = TokenManager()

    class Meta:
        db_table = "tokens"
        unique_together = ("user", "device_id")



# accounts/models.py

class SuperAdminUser(User):
    class Meta:
        proxy = True
        verbose_name = 'Super Admin'
        verbose_name_plural = 'Super Admins'


class BrandUser(User):
    class Meta:
        proxy = True
        verbose_name = 'Brand User'
        verbose_name_plural = 'Brand Users'


class TalentUser(User):
    class Meta:
        proxy = True
        verbose_name = 'Talent User'
        verbose_name_plural = 'Talent Users'


class BookmarkTalent(BaseModel):
    brand = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookmarked_talents')
    talent = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookmarked_by_brands')

    class Meta:
        unique_together = ('brand', 'talent')


class Role(BaseModel):
    """
    Role model for admin panel permissions
    """
    name = models.CharField(max_length=100, unique=True, help_text="Role name (e.g., 'Campaign Manager', 'User Manager')")
    description = models.TextField(blank=True, null=True, help_text="Description of the role")
    permissions = models.ManyToManyField(
        'auth.Permission',
        blank=True,
        help_text="Django permissions assigned to this role"
    )
    is_active = models.BooleanField(default=True, help_text="Whether this role is active")

    class Meta:
        verbose_name = 'Role'
        verbose_name_plural = 'Roles'
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

    @property
    def permission_count(self):
        """Get the number of permissions assigned to this role"""
        return self.permissions.count()


class UserRole(BaseModel):
    """
    User-Role assignment model
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='role_users')
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_roles',
        help_text="User who assigned this role"
    )
    is_active = models.BooleanField(default=True, help_text="Whether this role assignment is active")

    class Meta:
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'
        unique_together = ('user', 'role')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['role', 'is_active']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.role.name}"
