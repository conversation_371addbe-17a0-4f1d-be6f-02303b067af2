# Generated by Django 5.2.1 on 2025-05-31 05:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_expertisearea_language'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlatformCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='SocialMediaPlatform',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('icon', models.ImageField(blank=True, null=True, upload_to='platform_icons/')),
            ],
        ),
        migrations.CreateModel(
            name='SocialMediaCategoryRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('min_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('max_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('platform_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rates', to='accounts.platformcategory')),
            ],
        ),
        migrations.AddField(
            model_name='platformcategory',
            name='platform',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platforms', to='accounts.socialmediaplatform'),
        ),
    ]
