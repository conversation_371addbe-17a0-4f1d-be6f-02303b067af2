# Generated by Django 5.2.1 on 2025-06-02 06:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0005_bankaccountdetails_projects_socialconnects_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SocialRateCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('min_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('max_price', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
            options={
                'verbose_name': 'Social Rate Card',
                'verbose_name_plural': 'Social Rate Cards',
            },
        ),
        migrations.AlterModelOptions(
            name='socialmediacategoryrate',
            options={'verbose_name': 'Social Media Rate Card', 'verbose_name_plural': 'Social Media Rate Cards'},
        ),
        migrations.RemoveIndex(
            model_name='socialmediacategoryrate',
            name='accounts_so_user_id_35889e_idx',
        ),
        migrations.AlterUniqueTogether(
            name='socialmediacategoryrate',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='socialratecard',
            name='platform_category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='social_rate_cards', to='accounts.platformcategory'),
        ),
        migrations.AddField(
            model_name='socialratecard',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='social_rate_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.RemoveField(
            model_name='socialmediacategoryrate',
            name='user',
        ),
        migrations.AddIndex(
            model_name='socialratecard',
            index=models.Index(fields=['user', 'platform_category'], name='accounts_so_user_id_605b6d_idx'),
        ),
        migrations.AddIndex(
            model_name='socialratecard',
            index=models.Index(fields=['min_price', 'max_price'], name='accounts_so_min_pri_b692c7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='socialratecard',
            unique_together={('user', 'platform_category')},
        ),
    ]
