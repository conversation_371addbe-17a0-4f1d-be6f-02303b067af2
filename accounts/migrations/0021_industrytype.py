# Generated by Django 5.2.1 on 2025-06-13 09:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0020_alter_brandprofile_industry_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='IndustryType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Industry Type',
                'verbose_name_plural': 'Industry Types',
                'indexes': [models.Index(fields=['name'], name='accounts_in_name_0d1666_idx'), models.Index(fields=['is_active'], name='accounts_in_is_acti_c0c4bf_idx')],
            },
        ),
    ]
