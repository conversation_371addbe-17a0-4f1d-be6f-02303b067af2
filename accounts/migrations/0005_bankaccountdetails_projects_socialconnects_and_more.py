# Generated by Django 5.2.1 on 2025-06-02 03:23

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_platformcategory_socialmediaplatform_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BankAccountDetails',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account_number', models.CharField(max_length=20, validators=[django.core.validators.RegexValidator(message='Account number must contain only digits', regex='^\\d+$')])),
                ('ifsc_code', models.CharField(max_length=11, validators=[django.core.validators.RegexValidator(message='Please enter a valid IFSC code', regex='^[A-Z]{4}0[A-Z0-9]{6}$')])),
                ('account_holder_name', models.CharField(max_length=100)),
                ('aadhar_number', models.CharField(max_length=12, validators=[django.core.validators.RegexValidator(message='Please enter a valid 12-digit Aadhar number', regex='^\\d{12}$')])),
                ('pan_number', models.CharField(max_length=10, validators=[django.core.validators.RegexValidator(message='Please enter a valid PAN number', regex='^[A-Z]{5}[0-9]{4}[A-Z]{1}$')])),
            ],
            options={
                'verbose_name': 'Bank Account Detail',
                'verbose_name_plural': 'Bank Account Details',
            },
        ),
        migrations.CreateModel(
            name='Projects',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(blank=True, max_length=200, null=True)),
                ('description', models.TextField(blank=True, help_text='Maximum 2000 characters', max_length=2000, null=True)),
            ],
            options={
                'verbose_name': 'Project',
                'verbose_name_plural': 'Projects',
            },
        ),
        migrations.CreateModel(
            name='SocialConnects',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Social Connect',
                'verbose_name_plural': 'Social Connects',
            },
        ),
        migrations.CreateModel(
            name='TalentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('profile_pic', models.ImageField(blank=True, null=True, upload_to='profile_pics/')),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('gender', models.CharField(blank=True, choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('bio', models.TextField(blank=True, help_text='Maximum 1000 characters', max_length=1000, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('is_gst', models.BooleanField(default=False)),
                ('gst_number', models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator(message='Enter a valid GST number', regex='^\\d{2}[A-Z]{5}\\d{4}[A-Z]{1}[A-Z\\d]{1}[Z]{1}[A-Z\\d]{1}$')])),
            ],
            options={
                'verbose_name': 'Talent Profile',
                'verbose_name_plural': 'Talent Profiles',
            },
        ),
        migrations.AlterModelOptions(
            name='otp',
            options={'ordering': ['-created_at'], 'verbose_name': 'OTP', 'verbose_name_plural': 'OTPs'},
        ),
        migrations.AlterModelOptions(
            name='socialmediacategoryrate',
            options={'verbose_name': 'Social Rate Card', 'verbose_name_plural': 'Social Rate Cards'},
        ),
        migrations.AddField(
            model_name='socialmediacategoryrate',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='social_media_category_rates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='socialmediacategoryrate',
            unique_together={('user', 'platform_category')},
        ),
        migrations.AddIndex(
            model_name='socialmediacategoryrate',
            index=models.Index(fields=['user', 'platform_category'], name='accounts_so_user_id_35889e_idx'),
        ),
        migrations.AddIndex(
            model_name='socialmediacategoryrate',
            index=models.Index(fields=['min_price', 'max_price'], name='accounts_so_min_pri_f87816_idx'),
        ),
        migrations.AddField(
            model_name='bankaccountdetails',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_account_details', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='projects',
            name='social_tags',
            field=models.ManyToManyField(blank=True, related_name='projects', to='accounts.socialmediaplatform'),
        ),
        migrations.AddField(
            model_name='projects',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='socialconnects',
            name='platform',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='social_connects', to='accounts.socialmediaplatform'),
        ),
        migrations.AddField(
            model_name='socialconnects',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='social_connects', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='talentprofile',
            name='expertise_areas',
            field=models.ManyToManyField(blank=True, related_name='talent_profiles', to='accounts.expertisearea'),
        ),
        migrations.AddField(
            model_name='talentprofile',
            name='languages',
            field=models.ManyToManyField(blank=True, related_name='talent_profiles', to='accounts.language'),
        ),
        migrations.AddField(
            model_name='talentprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='talent_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='bankaccountdetails',
            index=models.Index(fields=['user', 'account_number'], name='accounts_ba_user_id_6ac5b1_idx'),
        ),
        migrations.AddIndex(
            model_name='bankaccountdetails',
            index=models.Index(fields=['aadhar_number'], name='accounts_ba_aadhar__66703c_idx'),
        ),
        migrations.AddIndex(
            model_name='bankaccountdetails',
            index=models.Index(fields=['pan_number'], name='accounts_ba_pan_num_8f3db9_idx'),
        ),
        migrations.AddIndex(
            model_name='bankaccountdetails',
            index=models.Index(fields=['ifsc_code'], name='accounts_ba_ifsc_co_06116f_idx'),
        ),
        migrations.AddIndex(
            model_name='bankaccountdetails',
            index=models.Index(fields=['created_at'], name='accounts_ba_created_b6069c_idx'),
        ),
        migrations.AddIndex(
            model_name='projects',
            index=models.Index(fields=['user', '-created_at'], name='accounts_pr_user_id_5aa25d_idx'),
        ),
        migrations.AddIndex(
            model_name='projects',
            index=models.Index(fields=['title'], name='accounts_pr_title_78326a_idx'),
        ),
        migrations.AddIndex(
            model_name='projects',
            index=models.Index(fields=['created_at'], name='accounts_pr_created_3bda35_idx'),
        ),
        migrations.AddIndex(
            model_name='socialconnects',
            index=models.Index(fields=['user', 'platform'], name='accounts_so_user_id_623e06_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='socialconnects',
            unique_together={('user', 'platform')},
        ),
        migrations.AddIndex(
            model_name='talentprofile',
            index=models.Index(fields=['user'], name='accounts_ta_user_id_413b9f_idx'),
        ),
        migrations.AddIndex(
            model_name='talentprofile',
            index=models.Index(fields=['name'], name='accounts_ta_name_f3f4e2_idx'),
        ),
        migrations.AddIndex(
            model_name='talentprofile',
            index=models.Index(fields=['state', 'city'], name='accounts_ta_state_b8fe28_idx'),
        ),
        migrations.AddIndex(
            model_name='talentprofile',
            index=models.Index(fields=['is_gst'], name='accounts_ta_is_gst_55e634_idx'),
        ),
        migrations.AddIndex(
            model_name='talentprofile',
            index=models.Index(fields=['gst_number'], name='accounts_ta_gst_num_fb55a3_idx'),
        ),
        migrations.AddIndex(
            model_name='talentprofile',
            index=models.Index(fields=['created_at'], name='accounts_ta_created_d257dd_idx'),
        ),
    ]
