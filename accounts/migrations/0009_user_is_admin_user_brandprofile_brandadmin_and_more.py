# Generated by Django 5.2.1 on 2025-06-02 15:13

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_projects_category_tags'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='is_admin_user',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='BrandProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brand_logo', models.ImageField(blank=True, null=True, upload_to='brand_logos/')),
                ('brand_name', models.CharField(max_length=100)),
                ('industry_type', models.Char<PERSON><PERSON>(max_length=100)),
                ('state', models.Char<PERSON>ield(max_length=100)),
                ('city', models.CharField(max_length=100)),
                ('gst_number', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator(message='Enter a valid GST number', regex='^\\d{2}[A-Z]{5}\\d{4}[A-Z]{1}[A-Z\\d]{1}[Z]{1}[A-Z\\d]{1}$')])),
                ('company_name_on_cin', models.CharField(max_length=200)),
                ('cin_number', models.CharField(max_length=21)),
                ('cin_certificate', models.FileField(blank=True, null=True, upload_to='cin_certificates/')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='brand_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Brand Profile',
                'verbose_name_plural': 'Brand Profiles',
            },
        ),
        migrations.CreateModel(
            name='BrandAdmin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(max_length=10, validators=[django.core.validators.RegexValidator(message='Enter a valid 10-digit mobile number.', regex='^\\d{10}$')])),
                ('is_primary', models.BooleanField(default=False)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='brand_admin_roles', to=settings.AUTH_USER_MODEL)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admins', to='accounts.brandprofile')),
            ],
            options={
                'verbose_name': 'Brand Admin',
                'verbose_name_plural': 'Brand Admins',
            },
        ),
        migrations.AddIndex(
            model_name='brandprofile',
            index=models.Index(fields=['user'], name='accounts_br_user_id_6d2fa9_idx'),
        ),
        migrations.AddIndex(
            model_name='brandprofile',
            index=models.Index(fields=['brand_name'], name='accounts_br_brand_n_04408f_idx'),
        ),
        migrations.AddIndex(
            model_name='brandprofile',
            index=models.Index(fields=['state', 'city'], name='accounts_br_state_1f5afd_idx'),
        ),
        migrations.AddIndex(
            model_name='brandprofile',
            index=models.Index(fields=['gst_number'], name='accounts_br_gst_num_827fb8_idx'),
        ),
        migrations.AddIndex(
            model_name='brandprofile',
            index=models.Index(fields=['created_at'], name='accounts_br_created_045611_idx'),
        ),
        migrations.AddIndex(
            model_name='brandadmin',
            index=models.Index(fields=['brand'], name='accounts_br_brand_i_3c2147_idx'),
        ),
        migrations.AddIndex(
            model_name='brandadmin',
            index=models.Index(fields=['email'], name='accounts_br_email_2bac16_idx'),
        ),
        migrations.AddIndex(
            model_name='brandadmin',
            index=models.Index(fields=['phone'], name='accounts_br_phone_120fe6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='brandadmin',
            unique_together={('brand', 'email')},
        ),
    ]
