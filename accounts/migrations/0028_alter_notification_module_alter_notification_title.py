# Generated by Django 5.2.1 on 2025-07-02 05:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0027_language_value_alter_notification_module_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notification',
            name='module',
            field=models.IntegerField(choices=[(1, 'Talent Applied'), (2, 'Talent Received Proposal'), (3, 'Brand Send Revised Proposal'), (4, 'Talent Counter Proposal Received'), (5, 'Proposal Accepted By Brand'), (6, 'Proposal Accepted By Talent'), (7, 'Script Sent by Brand'), (8, 'Script Received by Talent'), (9, 'Script Revised by Brand'), (10, 'Feedback Sent by Talent'), (11, 'Script Finalised'), (12, 'Script Terminated'), (13, 'Agreement Sent by Brand'), (14, 'Agreement Feedback by Talent'), (15, 'Agreement Accepted'), (16, 'Agreement Rejected'), (17, 'Agreement Terminated')], null=True),
        ),
        migrations.AlterField(
            model_name='notification',
            name='title',
            field=models.IntegerField(blank=True, choices=[(1, 'Talent Applied'), (2, 'Talent Received Proposal'), (3, 'Brand Send Revised Proposal'), (4, 'Talent Counter Proposal Received'), (5, 'Proposal Accepted By Brand'), (6, 'Proposal Accepted By Talent'), (7, 'Script Sent by Brand'), (8, 'Script Received by Talent'), (9, 'Script Revised by Brand'), (10, 'Feedback Sent by Talent'), (11, 'Script Finalised'), (12, 'Script Terminated'), (13, 'Agreement Sent by Brand'), (14, 'Agreement Feedback by Talent'), (15, 'Agreement Accepted'), (16, 'Agreement Rejected'), (17, 'Agreement Terminated')], null=True),
        ),
    ]
