# Generated by Django 5.2.1 on 2025-06-06 12:34

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0013_user_kyc_status_panverification_aadhaarclient'),
    ]

    operations = [
        migrations.AlterField(
            model_name='bankaccountdetails',
            name='aadhar_number',
            field=models.CharField(blank=True, max_length=12, null=True, validators=[django.core.validators.RegexValidator(message='Please enter a valid 12-digit Aadhar number', regex='^\\d{12}$')]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='bankaccountdetails',
            name='account_holder_name',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='bankaccountdetails',
            name='account_number',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator(message='Account number must contain only digits', regex='^\\d+$')]),
        ),
        migrations.AlterField(
            model_name='bankaccountdetails',
            name='ifsc_code',
            field=models.CharField(blank=True, max_length=11, null=True, validators=[django.core.validators.RegexValidator(message='Please enter a valid IFSC code', regex='^[A-Z]{4}0[A-Z0-9]{6}$')]),
        ),
        migrations.AlterField(
            model_name='bankaccountdetails',
            name='pan_number',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator(message='Please enter a valid PAN number', regex='^[A-Z]{5}[0-9]{4}[A-Z]{1}$')]),
        ),
    ]
