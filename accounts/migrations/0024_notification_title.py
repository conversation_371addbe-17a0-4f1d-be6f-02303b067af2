# Generated by Django 5.2.1 on 2025-06-24 11:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0023_branduser_superadminuser_talentuser_bookmarktalent'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='title',
            field=models.CharField(blank=True, choices=[('talent_applied', 'Talent Applied'), ('received_proposal', 'Received Proposal'), ('brand_revised_proposal', 'Brand Revised Proposal'), ('counter_proposal_received', 'Counter Proposal Received'), ('proposal_accepted_by_brand', 'Proposal Accepted By Brand'), ('proposal_accepted_by_talent', 'Proposal Accepted By Talent')], max_length=255, null=True),
        ),
    ]
