# Generated by Django 5.2.1 on 2025-06-12 09:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0017_brandprofile_brand_admin_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('module', models.CharField(choices=[('talent_applied', 'Talent Applied'), ('received_proposal', 'Received Proposal'), ('brand_revised_proposal', 'Brand Revised Proposal'), ('counter_proposal_received', 'Counter Proposal Received'), ('proposal_accepted_by_brand', 'Proposal Accepted By Brand'), ('proposal_accepted_by_talent', 'Proposal Accepted By Talent')], max_length=255, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('message', models.CharField(max_length=255)),
                ('extra_info', models.JSONField(default=dict)),
                ('is_triggered', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'notifications',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='Token',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created')),
                ('key', models.CharField(db_index=True, max_length=40, primary_key=True, serialize=False, unique=True)),
                ('device_id', models.CharField(blank=True, db_index=True, editable=False, max_length=64, null=True, verbose_name='device_id')),
                ('device_name', models.CharField(blank=True, max_length=255, null=True)),
                ('device_os', models.CharField(blank=True, max_length=255, null=True)),
                ('registration_id', models.CharField(blank=True, max_length=255, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tokens', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'db_table': 'tokens',
                'unique_together': {('user', 'device_id')},
            },
        ),
    ]
