# Generated by Django 5.2.1 on 2025-06-09 09:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0015_remove_projects_images_remove_projects_videos_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='socialconnects',
            name='access_token',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='socialconnects',
            name='platform_user_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='socialconnects',
            name='refresh_token',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='socialconnects',
            name='token_expires_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='SocialMediaAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('followers_count', models.IntegerField(default=0)),
                ('following_count', models.IntegerField(default=0)),
                ('posts_count', models.IntegerField(default=0)),
                ('average_reach', models.IntegerField(default=0)),
                ('average_engagement', models.FloatField(default=0)),
                ('social_connect', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='accounts.socialconnects')),
            ],
            options={
                'verbose_name': 'Social Media Analytics',
                'verbose_name_plural': 'Social Media Analytics',
            },
        ),
        migrations.CreateModel(
            name='SocialMediaPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('post_id', models.CharField(max_length=255)),
                ('post_url', models.URLField()),
                ('post_type', models.CharField(max_length=50)),
                ('reach', models.IntegerField(default=0)),
                ('engagement', models.IntegerField(default=0)),
                ('likes', models.IntegerField(default=0)),
                ('comments', models.IntegerField(default=0)),
                ('shares', models.IntegerField(default=0)),
                ('posted_at', models.DateTimeField()),
                ('analytics', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='posts', to='accounts.socialmediaanalytics')),
            ],
            options={
                'verbose_name': 'Social Media Post',
                'verbose_name_plural': 'Social Media Posts',
            },
        ),
        migrations.AddIndex(
            model_name='socialmediaanalytics',
            index=models.Index(fields=['social_connect'], name='accounts_so_social__bd326e_idx'),
        ),
        migrations.AddIndex(
            model_name='socialmediaanalytics',
            index=models.Index(fields=['created_at'], name='accounts_so_created_ccef7a_idx'),
        ),
        migrations.AddIndex(
            model_name='socialmediapost',
            index=models.Index(fields=['analytics', '-posted_at'], name='accounts_so_analyti_f34c7f_idx'),
        ),
        migrations.AddIndex(
            model_name='socialmediapost',
            index=models.Index(fields=['post_id'], name='accounts_so_post_id_b3a281_idx'),
        ),
        migrations.AddIndex(
            model_name='socialmediapost',
            index=models.Index(fields=['post_type'], name='accounts_so_post_ty_cd7066_idx'),
        ),
    ]
