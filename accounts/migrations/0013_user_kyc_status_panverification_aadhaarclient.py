# Generated by Django 5.2.1 on 2025-06-04 17:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0012_socialconnects_connected_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='kyc_status',
            field=models.IntegerField(choices=[(0, 'Not Started'), (1, 'In Progress'), (2, 'Verified')], default=0),
        ),
        migrations.CreateModel(
            name='PANVerification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pan_number', models.CharField(max_length=20)),
                ('client_id', models.Char<PERSON>ield(max_length=100)),
                ('full_name', models.CharField(max_length=100)),
                ('full_name_split', models.JSONField(blank=True, null=True)),
                ('masked_aadhaar', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True)),
                ('gender', models.CharField(blank=True, max_length=10, null=True)),
                ('dob', models.CharField(blank=True, max_length=20, null=True)),
                ('is_pan_verified', models.BooleanField(default=False)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pan_verification', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AadhaarClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('aadhaar_number', models.CharField(blank=True, max_length=12, null=True)),
                ('client_id', models.CharField(max_length=255)),
                ('full_name', models.CharField(blank=True, max_length=255, null=True)),
                ('dob', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, max_length=10, null=True)),
                ('is_aadhar_verified', models.BooleanField(default=False)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='aadhar_verification', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Aadhaar Client',
                'verbose_name_plural': 'Aadhaar Clients',
                'unique_together': {('user', 'aadhaar_number')},
            },
        ),
    ]
