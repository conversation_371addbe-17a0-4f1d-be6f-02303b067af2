# Generated by Django 5.2.1 on 2025-06-18 17:26

import django.contrib.auth.models
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0022_alter_brandprofile_cin_number_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BrandUser',
            fields=[
            ],
            options={
                'verbose_name': 'Brand User',
                'verbose_name_plural': 'Brand Users',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('accounts.user',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='SuperAdminUser',
            fields=[
            ],
            options={
                'verbose_name': 'Super Admin',
                'verbose_name_plural': 'Super Admins',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('accounts.user',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='TalentUser',
            fields=[
            ],
            options={
                'verbose_name': 'Talent User',
                'verbose_name_plural': 'Talent Users',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('accounts.user',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='BookmarkTalent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookmarked_talents', to=settings.AUTH_USER_MODEL)),
                ('talent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookmarked_by_brands', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('brand', 'talent')},
            },
        ),
    ]
