# Generated by Django 5.2.1 on 2025-06-13 13:21

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0021_industrytype'),
    ]

    operations = [
        migrations.AlterField(
            model_name='brandprofile',
            name='cin_number',
            field=models.Char<PERSON>ield(blank=True, max_length=21, null=True),
        ),
        migrations.AlterField(
            model_name='brandprofile',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='brandprofile',
            name='company_name_on_cin',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='brandprofile',
            name='gst_number',
            field=models.Char<PERSON>ield(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator(message='Enter a valid GST number', regex='^\\d{2}[A-Z]{5}\\d{4}[A-Z]{1}[A-Z\\d]{1}[Z]{1}[A-Z\\d]{1}$')]),
        ),
        migrations.AlterField(
            model_name='brandprofile',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
