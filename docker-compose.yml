version: '3.8'

services:
  # Redis for WebSocket support
  redis:
    image: redis
    container_name: redis
    ports:
      - "6379:6379"
    restart: unless-stopped
    volumes:
      - redis_data:/data

  # Django Backend Server with ASGI support
  backend_server:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - ALLOWED_HOSTS=*
    env_file:
      - ./talent_trade/.env
    command: >
      sh -c "python manage.py migrate &&
             gunicorn talent_trade.wsgi:application --bind 0.0.0.0:8000"

  django-socket:
    build: .
    container_name: django_socket
    command: daphne -b 0.0.0.0 -p 8001 talent_trade.asgi:application
    volumes:
      - .:/code
    ports:
      - 8001:8001
    depends_on:
      - redis
    restart: always

volumes:
  redis_data: