from rest_framework import serializers

from accounts.choices import UserTypeChoices
from accounts.models import TalentProfile
from .models import RoomMessage, Agreement
from decimal import Decimal

class RoomMessageSerializer(serializers.ModelSerializer):
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    sender_email = serializers.CharField(source='sender.email', read_only=True)
    sender_role = serializers.SerializerMethodField()
    
    class Meta:
        model = RoomMessage
        fields = [
            'id',
            'room',
            'message',
            'message_type',
            'sender',
            'sender_name',
            'sender_email',
            'sender_role',
            'is_read_by',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'sender', 'sender_name', 'sender_email', 'sender_role', 'is_read_by', 'created_at', 'updated_at']
    
    def get_sender_role(self, obj):
        """Get sender role in the room"""
        try:
            from campaigns.models import RoomParticipant
            participant = RoomParticipant.objects.get(
                room=obj.room,
                user=obj.sender
            )
            return participant.role
        except RoomParticipant.DoesNotExist:
            return 'unknown'

# class AgreementSerializer(serializers.ModelSerializer):
#     sent_by_name = serializers.CharField(source='sent_by.get_full_name', read_only=True)
#     sent_to_name = serializers.CharField(source='sent_to.get_full_name', read_only=True)
#     file_url = serializers.FileField(source='file', read_only=True)

#     class Meta:
#         model = Agreement
#         fields = [
#             'id', 'proposal', 'file', 'file_url', 'status', 'feedback', 'sent_by', 'sent_by_name', 'sent_to', 'sent_to_name', 'version', 'created_at', 'updated_at'
#         ]
#         read_only_fields = ['id', 'sent_by_name', 'sent_to_name', 'file_url', 'created_at', 'updated_at'] 
        


class AgreementSerializer(serializers.ModelSerializer):
    talent_id = serializers.IntegerField(required=False)
    campaign_name = serializers.SerializerMethodField()
    sent_by_name = serializers.SerializerMethodField()
    sent_to_name = serializers.SerializerMethodField()
    campaign_id = serializers.SerializerMethodField()
    file = serializers.CharField(required=False)

    class Meta:
        model = Agreement
        fields = [
            'id', 'proposal_version', 'campaign_id', 'campaign_name',
            'file', 'feedback', 'content', 'talent_id',
            'sent_by', 'sent_by_name',
            'sent_to', 'sent_to_name', 'status',
            'version', 'accepted_by', 'rejected_by', 'terminated_by',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at',
            'sent_by', 'sent_to', 'version',
            'accepted_by', 'rejected_by', 'terminated_by'
        ]

    def get_campaign_id(self, obj):
        return getattr(obj.proposal_version.proposal.campaign, 'id', None)

    def get_campaign_name(self, obj):
        return getattr(obj.proposal_version.proposal.campaign, 'campaign_name', None)

    def get_sent_by_name(self, obj):
        user = obj.sent_by
        if not user:
            return None
        if user.user_type == UserTypeChoices.BRAND:
            return getattr(user.brand_profile, "brand_admin_name", None)
        elif user.user_type == UserTypeChoices.TALENT:
            return getattr(user.talent_profile, "name", None)
        return None

    def get_sent_to_name(self, obj):
        user = obj.sent_to
        if not user:
            return None
        if user.user_type == UserTypeChoices.BRAND:
            return getattr(user.brand_profile, "brand_admin_name", None)
        elif user.user_type == UserTypeChoices.TALENT:
            return getattr(user.talent_profile, "name", None)
        return None
    
    def get_file(self,obj):
        return obj.file.name if obj.file else None


    def create(self, validated_data):
        request = self.context.get("request")
        user = request.user
        proposal_version = validated_data.get("proposal_version")
        feedback = validated_data.get("feedback")
        file = validated_data.get("file")
        user_type = getattr(user, "user_type", "")
        talent_id = validated_data.pop("talent_id", None)

        # Determine recipient based on sender role
        if user_type == UserTypeChoices.BRAND.value[0]:
            if not talent_id:
                raise serializers.ValidationError({
                    "talent_id": "This field is required when brand is sending agreement."
                })
            try:
                talent_profile = TalentProfile.objects.get(id=talent_id)
            except TalentProfile.DoesNotExist:
                raise serializers.ValidationError({
                    "talent_id": "Talent profile not found."
                })
            sent_to_user = talent_profile.user
        elif user_type == UserTypeChoices.TALENT.value[0]:
            try:
                sent_to_user = proposal_version.proposal.campaign.brand
            except AttributeError:
                raise serializers.ValidationError({
                    "proposal_version": "Invalid proposal version or missing campaign/brand."
                })
        else:
            raise serializers.ValidationError({"user_type": "Unsupported user type."})

        # Auto-increment version
        latest_agreement = Agreement.objects.filter(proposal_version=proposal_version).order_by('-version').first()
        new_version = round(float(latest_agreement.version) + 0.1, 1) if latest_agreement else 1.0

        return Agreement.objects.create(
            proposal_version=proposal_version,
            file=file,
            feedback=feedback,
            sent_by=user,
            sent_to=sent_to_user,
            version=new_version
        )
