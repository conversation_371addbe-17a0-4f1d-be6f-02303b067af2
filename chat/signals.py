from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from .models import Agreement
from accounts.models import Notification
from talent_trade.utils.choices import NotificationModuleChoice, AgreementStatusChoices
from accounts.choices import UserTypeChoices


@receiver(post_save, sender=Agreement)
def create_agreement_notification(sender, instance, created, **kwargs):
    """
    Create notification when an Agreement is created
    """
    if created:
        campaign_name = (
            instance.proposal_version.proposal.campaign.campaign_name
            if instance.proposal_version
            and instance.proposal_version.proposal
            and instance.proposal_version.proposal.campaign
            else None
        )

        extra_info = {
            "agreement_id": instance.id,
            "version": str(instance.version),
            "proposal_version_id": instance.proposal_version_id,
            "campaign_name": campaign_name,
        }

        if instance.sent_by.user_type == UserTypeChoices.BRAND.get_value("BRAND"):
            Notification.objects.create(
                user=instance.sent_to,
                module=NotificationModuleChoice.AGREEMENT_SENT_BY_BRAND,
                title=NotificationModuleChoice.AGREEMENT_SENT_BY_BRAND,
                message=f"Brand has sent you an agreement for review for campaign {campaign_name}.",
                extra_info=extra_info,
            )

        elif instance.sent_by.user_type == UserTypeChoices.TALENT.get_value("TALENT"):
            talent_name = getattr(instance.sent_by.talent_profile, "name", instance.sent_by.get_full_name())
            Notification.objects.create(
                user=instance.sent_to,
                module=NotificationModuleChoice.AGREEMENT_FEEDBACK_BY_TALENT,
                title=NotificationModuleChoice.AGREEMENT_FEEDBACK_BY_TALENT,
                message=f"{talent_name} has shared feedback on the agreement for campaign {campaign_name}.",
                extra_info=extra_info,
            )


@receiver(pre_save, sender=Agreement)
def handle_agreement_status_update(sender, instance, **kwargs):
    if not instance.pk:
        return  # New object

    previous = Agreement.objects.get(pk=instance.pk)
    if previous.status == instance.status:
        return

    actor = None
    message = ""
    module = None

    campaign_name = (
        instance.proposal_version.proposal.campaign.campaign_name
        if instance.proposal_version
        and instance.proposal_version.proposal
        and instance.proposal_version.proposal.campaign
        else None
    )

    if instance.status == AgreementStatusChoices.ACCEPTED:
        actor = instance.accepted_by
        name = _get_user_name(actor)
        message = f"Agreement has been accepted by {name} for campaign {campaign_name}."
        module = NotificationModuleChoice.AGREEMENT_ACCEPTED

    elif instance.status == AgreementStatusChoices.TERMINATED:
        actor = instance.terminated_by
        name = _get_user_name(actor)
        message = f"Agreement has been terminated by {name} for campaign {campaign_name}."
        module = NotificationModuleChoice.AGREEMENT_TERMINATED

    elif instance.status == AgreementStatusChoices.REJECTED:
        actor = instance.rejected_by
        name = _get_user_name(actor)
        message = f"Agreement has been rejected by {name} for campaign {campaign_name}."
        module = NotificationModuleChoice.AGREEMENT_REJECTED

    if actor and module:
        Notification.objects.create(
            user=instance.sent_to if actor == instance.sent_by else instance.sent_by,
            module=module,
            title=module,
            message=message,
            extra_info={
                "agreement_id": instance.id,
                "version": str(instance.version),
                "proposal_version_id": instance.proposal_version_id,
                "campaign_name": campaign_name,
            },
        )


def _get_user_name(user):
    if not user:
        return "Unknown"
    if user.user_type == UserTypeChoices.BRAND.get_value("BRAND"):
        return getattr(user.brand_profile, "brand_admin_name", user.get_full_name())
    elif user.user_type == UserTypeChoices.TALENT.get_value("TALENT"):
        return getattr(user.talent_profile, "name", user.get_full_name())
    return user.get_full_name()
