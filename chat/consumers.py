import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone
from campaigns.models import Room, RoomParticipant
from .models import RoomMessage


class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'chat_{self.room_id}'
        
        # Check if user is authenticated
        if not self.scope['user'].is_authenticated:
            await self.close()
            return
        
        # Check if user is a participant in this room
        is_participant = await self.is_room_participant(self.room_id, self.scope['user'])
        if not is_participant:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'room_id': self.room_id,
            'message': 'Connected to chat room'
        }))

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type', 'chat_message')
        
        if message_type == 'chat_message':
            await self.handle_chat_message(text_data_json)
        elif message_type == 'typing':
            await self.handle_typing(text_data_json)
        elif message_type == 'read_messages':
            await self.handle_read_messages(text_data_json)

    async def handle_chat_message(self, data):
        content = data.get('content', '')
        message_type = data.get('message_type', 'text')
        file_url = data.get('file_url', None)
        
        if not content and message_type == 'text':
            return
        
        # Save message to database
        saved_message = await self.save_message_to_db(content, message_type)
        
        if not saved_message:
            return
        
        # Get sender info
        sender_name = await self.get_sender_name(self.scope['user'])
        sender_role = await self.get_sender_role(self.scope['user'])
        
        # Update room's last_message_at
        await self.update_room_last_message()
        
        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': {
                    'id': saved_message.id,
                    'content': content,
                    'message_type': message_type,
                    'file_url': file_url,
                    'sender_id': self.scope['user'].id,
                    'sender_name': sender_name,
                    'sender_email': self.scope['user'].email,
                    'sender_role': sender_role,
                    'created_at': saved_message.created_at.isoformat(),
                    'is_read_by': saved_message.is_read_by
                }
            }
        )

    async def handle_typing(self, data):
        is_typing = data.get('is_typing', False)
        
        # Send typing indicator to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_typing',
                'user_id': self.scope['user'].id,
                'user_name': await self.get_sender_name(self.scope['user']),
                'is_typing': is_typing
            }
        )

    async def handle_read_messages(self, data):
        # Update participant's last_read_at
        await self.update_participant_last_read()
        
        # Send read confirmation to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'messages_read',
                'user_id': self.scope['user'].id,
                'user_name': await self.get_sender_name(self.scope['user'])
            }
        )

    async def chat_message(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message']
        }))

    async def user_typing(self, event):
        # Send typing indicator to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'user_typing',
            'user_id': event['user_id'],
            'user_name': event['user_name'],
            'is_typing': event['is_typing']
        }))

    async def messages_read(self, event):
        # Send read confirmation to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'messages_read',
            'user_id': event['user_id'],
            'user_name': event['user_name']
        }))

    @database_sync_to_async
    def save_message_to_db(self, content, message_type):
        """Save message to database"""
        try:
            room = Room.objects.get(id=self.room_id)
            message = RoomMessage.objects.create(
                room=room,
                message=content,
                message_type=message_type,
                sender=self.scope['user']
            )
            return message
        except Exception as e:
            print(f"Error saving message: {e}")
            return None

    @database_sync_to_async
    def is_room_participant(self, room_id, user):
        try:
            return RoomParticipant.objects.filter(
                room_id=room_id,
                user=user,
                is_active=True
            ).exists()
        except:
            return False

    @database_sync_to_async
    def update_room_last_message(self):
        try:
            room = Room.objects.get(id=self.room_id)
            room.last_message_at = timezone.now()
            room.save()
        except Room.DoesNotExist:
            pass

    @database_sync_to_async
    def get_sender_name(self, user):
        try:
            if user.user_type == 'B':  # Brand
                return user.brand_profile.brand_name
            else:  # Talent
                return user.talent_profile.name
        except:
            return user.email

    @database_sync_to_async
    def get_sender_role(self, user):
        try:
            participant = RoomParticipant.objects.get(
                room_id=self.room_id,
                user=user
            )
            return participant.role
        except RoomParticipant.DoesNotExist:
            return 'unknown'

    @database_sync_to_async
    def update_participant_last_read(self):
        try:
            participant = RoomParticipant.objects.get(
                room_id=self.room_id,
                user=self.scope['user']
            )
            participant.last_read_at = timezone.now()
            participant.save()
        except RoomParticipant.DoesNotExist:
            pass 