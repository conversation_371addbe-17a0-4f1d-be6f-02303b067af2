# Generated by Django 5.2.1 on 2025-06-30 07:10

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0003_agreement'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='agreement',
            name='accepted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='accepted_agreements', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='agreement',
            name='rejected_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rejected_agreements', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='agreement',
            name='terminated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='terminated_agreements', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='agreement',
            name='version',
            field=models.DecimalField(decimal_places=1, default=Decimal('1.0'), max_digits=4),
        ),
    ]
