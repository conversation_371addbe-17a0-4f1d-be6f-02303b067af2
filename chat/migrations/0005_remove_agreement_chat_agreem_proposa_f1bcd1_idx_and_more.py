# Generated by Django 5.2.1 on 2025-07-01 04:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0017_campaign_campaign_type_campaignproposalproduct_and_more'),
        ('chat', '0004_agreement_accepted_by_agreement_rejected_by_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='agreement',
            name='chat_agreem_proposa_f1bcd1_idx',
        ),
        migrations.RemoveIndex(
            model_name='agreement',
            name='chat_agreem_sent_by_832a2b_idx',
        ),
        migrations.RemoveIndex(
            model_name='agreement',
            name='chat_agreem_sent_to_4b705c_idx',
        ),
        migrations.RemoveIndex(
            model_name='agreement',
            name='chat_agreem_status_0539d3_idx',
        ),
        migrations.AlterUniqueTogether(
            name='agreement',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='agreement',
            name='proposal_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='agreements', to='campaigns.proposalversion'),
        ),
        migrations.AlterField(
            model_name='agreement',
            name='file',
            field=models.FileField(blank=True, null=True, upload_to='agreements/'),
        ),
        migrations.AlterField(
            model_name='agreement',
            name='status',
            field=models.IntegerField(choices=[(1, 'Sent'), (2, 'Accepted'), (3, 'Rejected'), (4, 'Terminated')], default=1),
        ),
        migrations.RemoveField(
            model_name='agreement',
            name='feedback',
        ),
        migrations.RemoveField(
            model_name='agreement',
            name='proposal',
        ),
    ]
