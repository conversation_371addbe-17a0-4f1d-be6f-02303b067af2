# Generated by Django 5.2.1 on 2025-06-24 09:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0015_rename_location_is_negotiable_proposal_deliverable_is_negotiable'),
        ('chat', '0002_roommessage_message_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Agreement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('file', models.FileField(upload_to='agreements/')),
                ('status', models.CharField(choices=[('SENT', 'Sent'), ('ACCEPTED', 'Accepted'), ('REJECTED', 'Rejected'), ('TERMINATED', 'Terminated')], default='SENT', max_length=20)),
                ('feedback', models.TextField(blank=True, null=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('proposal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='agreements', to='campaigns.proposal')),
                ('sent_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_agreements', to=settings.AUTH_USER_MODEL)),
                ('sent_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_agreements', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-version', '-created_at'],
                'indexes': [models.Index(fields=['proposal', '-version'], name='chat_agreem_proposa_f1bcd1_idx'), models.Index(fields=['sent_by', '-created_at'], name='chat_agreem_sent_by_832a2b_idx'), models.Index(fields=['sent_to', '-created_at'], name='chat_agreem_sent_to_4b705c_idx'), models.Index(fields=['status'], name='chat_agreem_status_0539d3_idx')],
                'unique_together': {('proposal', 'version')},
            },
        ),
    ]
