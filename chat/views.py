from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from campaigns.models import Room, RoomParticipant
from talent_trade.utils.choices import AgreementStatusChoices
from .models import RoomMessage, Agreement
from .serializers import RoomMessageSerializer, AgreementSerializer
from accounts.responses import Responses
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework import viewsets, mixins
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action



@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_room_messages(request, room_id):
    """Get all messages for a specific room"""
    try:
        # Check if room exists and user is participant
        room = get_object_or_404(Room, id=room_id, is_active=True)
        is_participant = RoomParticipant.objects.filter(
            room=room,
            user=request.user,
            is_active=True
        ).exists()
        
        if not is_participant:
            return Response(
                {"detail": "You don't have permission to access this room"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get messages for the room
        messages = RoomMessage.objects.filter(room=room).order_by('created_at')
        serializer = RoomMessageSerializer(messages, many=True)
        
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Messages retrieved successfully',
            data=serializer.data
        )
        
    except Exception as e:
        return Response(
            {"detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_detailed_room_messages(request, room_id):
    """Get detailed messages with read status for both talent and brand sides"""
    try:
        # Check if room exists and user is participant
        room = get_object_or_404(Room, id=room_id, is_active=True)
        participant = RoomParticipant.objects.filter(
            room=room,
            user=request.user,
            is_active=True
        ).first()
        
        if not participant:
            return Response(
                {"detail": "You don't have permission to access this room"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get messages for the room
        messages = RoomMessage.objects.filter(room=room).order_by('created_at')
        
        # Get room participants
        room_participants = RoomParticipant.objects.filter(room=room, is_active=True)
        talent_participant = room_participants.filter(role='talent').first()
        brand_participant = room_participants.filter(role='brand').first()
        
        # Prepare detailed message data
        detailed_messages = []
        for message in messages:
            message_data = {
                'id': message.id,
                'content': message.message,
                'message_type': message.message_type,
                'sender_id': message.sender.id,
                'sender_name': message.sender.get_full_name() or message.sender.email,
                'sender_email': message.sender.email,
                'sender_role': participant.role if message.sender == request.user else (
                    'talent' if talent_participant and message.sender == talent_participant.user else 'brand'
                ),
                'created_at': message.created_at.isoformat(),
                'updated_at': message.updated_at.isoformat(),
                'read_status': {
                    'read_by_talent': message.is_read_by == 'talent',
                    'read_by_brand': message.is_read_by == 'brand',
                    'read_by_current_user': message.is_read_by == participant.role
                }
            }
            detailed_messages.append(message_data)
        
        # Prepare room summary
        room_summary = {
            'room_id': room.id,
            'room_name': room.name,
            'total_messages': len(detailed_messages),
            'unread_by_talent': len([m for m in messages if m.is_read_by != 'talent']),
            'unread_by_brand': len([m for m in messages if m.is_read_by != 'brand']),
            'participants': {
                'talent': {
                    'user_id': talent_participant.user.id if talent_participant else None,
                    'name': talent_participant.user.get_full_name() if talent_participant else None,
                    'email': talent_participant.user.email if talent_participant else None
                },
                'brand': {
                    'user_id': brand_participant.user.id if brand_participant else None,
                    'name': brand_participant.user.get_full_name() if brand_participant else None,
                    'email': brand_participant.user.email if brand_participant else None
                }
            }
        }
        
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Detailed messages retrieved successfully',
            data={
                'room_summary': room_summary,
                'messages': detailed_messages
            }
        )
        
    except Exception as e:
        return Response(
            {"detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def save_message(request, room_id):
    """Save a new message to a specific room"""
    try:
        # Check if room exists and user is participant
        room = get_object_or_404(Room, id=room_id, is_active=True)
        participant = RoomParticipant.objects.filter(
            room=room,
            user=request.user,
            is_active=True
        ).first()
        
        if not participant:
            return Response(
                {"detail": "You don't have permission to access this room"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get message content and type from request
        message_content = request.data.get('message', '').strip()
        message_type = request.data.get('message_type', 'text')
        
        if not message_content:
            return Response(
                {"detail": "Message content is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Save the message
        message = RoomMessage.objects.create(
            room=room,
            message=message_content,
            message_type=message_type,
            sender=request.user
        )
        
        # Serialize the saved message
        serializer = RoomMessageSerializer(message)
        
        return Responses.success_response(
            status=status.HTTP_201_CREATED,
            message='Message saved successfully',
            data=serializer.data
        )
        
    except Exception as e:
        return Response(
            {"detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_messages_as_read(request, room_id):
    """Mark messages as read by the current user"""
    try:
        # Check if room exists and user is participant
        room = get_object_or_404(Room, id=room_id, is_active=True)
        participant = RoomParticipant.objects.filter(
            room=room,
            user=request.user,
            is_active=True
        ).first()
        
        if not participant:
            return Response(
                {"detail": "You don't have permission to access this room"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get message IDs to mark as read
        message_ids = request.data.get('message_ids', [])
        
        if not message_ids:
            return Response(
                {"detail": "message_ids is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Determine user role (talent or brand)
        user_role = 'talent' if participant.role == 'talent' else 'brand'
        
        # Mark messages as read
        messages = RoomMessage.objects.filter(
            room=room,
            id__in=message_ids
        )
        
        for message in messages:
            if message.is_read_by != user_role:
                message.is_read_by = user_role
                message.save()
        
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Messages marked as read successfully'
        )
        
    except Exception as e:
        return Response(
            {"detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_message(request, message_id):
    """Delete a message (hard delete)"""
    try:
        message = get_object_or_404(RoomMessage, id=message_id)
        
        # Check if user is the sender or has permission
        if message.sender != request.user:
            return Response(
                {"detail": "You can only delete your own messages"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Hard delete the message
        message.delete()
        
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Message deleted successfully'
        )
        
    except Exception as e:
        return Response(
            {"detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_rooms_with_messages(request):
    """Get all rooms for the current user with message counts"""
    try:
        # Get all rooms where user is a participant
        user_rooms = RoomParticipant.objects.filter(
            user=request.user,
            is_active=True
        ).select_related('room')
        
        rooms_data = []
        for participant in user_rooms:
            room = participant.room
            if not room.is_active:
                continue
                
            # Get message count for this room
            message_count = RoomMessage.objects.filter(room=room).count()
            
            # Get last message
            last_message = RoomMessage.objects.filter(room=room).order_by('-created_at').first()
            
            # Get unread count for current user
            user_role = 'talent' if participant.role == 'talent' else 'brand'
            unread_count = RoomMessage.objects.filter(
                room=room,
                is_read_by__isnull=True
            ).count()
            
            room_data = {
                'room_id': room.id,
                'room_name': room.name,
                'user_role': participant.role,
                'total_messages': message_count,
                'unread_messages': unread_count,
                'last_message': {
                    'content': last_message.message if last_message else None,
                    'sender_name': last_message.sender.get_full_name() if last_message else None,
                    'created_at': last_message.created_at.isoformat() if last_message else None
                } if last_message else None,
                'created_at': room.created_at.isoformat(),
                'updated_at': room.updated_at.isoformat()
            }
            rooms_data.append(room_data)
        
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='User rooms retrieved successfully',
            data=rooms_data
        )
        
    except Exception as e:
        return Response(
            {"detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# class AgreementViewSet(mixins.CreateModelMixin,
#                       mixins.ListModelMixin,
#                       mixins.UpdateModelMixin,
#                       viewsets.GenericViewSet):
#     queryset = Agreement.objects.all()
#     serializer_class = AgreementSerializer
#     permission_classes = [IsAuthenticated]
#     parser_classes = [MultiPartParser, FormParser]
#     lookup_field = 'id'

#     def get_queryset(self):
#         proposal_id = self.request.query_params.get('proposal')
#         qs = Agreement.objects.all()
#         if proposal_id:
#             qs = qs.filter(proposal_id=proposal_id)
#         return qs.order_by('-version')

#     def create(self, request, *args, **kwargs):
#         serializer = self.get_serializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         serializer.save(sent_by=request.user)
#         return Responses.success_response(
#             status=status.HTTP_201_CREATED,
#             message='Agreement uploaded successfully',
#             data=serializer.data
#         )

#     def partial_update(self, request, *args, **kwargs):
#         agreement = self.get_object()
#         status_value = request.data.get('status')
#         if status_value and status_value not in AgreementStatus.values:
#             return Response({'detail': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)
#         serializer = self.get_serializer(agreement, data=request.data, partial=True)
#         serializer.is_valid(raise_exception=True)
#         serializer.save()
#         return Responses.success_response(
#             status=status.HTTP_200_OK,
#             message='Agreement updated',
#             data=serializer.data
#         ) 
    

class AgreementAPIView(ModelViewSet):
    """ViewSet for handling agreements"""
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'
    serializer_class = AgreementSerializer

    def get_queryset(self):
        queryset = Agreement.objects.select_related(
            "proposal_version",
            "proposal_version__proposal",
            "proposal_version__proposal__campaign",
            "sent_by",
            "sent_to"
        )

        proposal_version_id = self.request.query_params.get("proposal_version")
        if proposal_version_id:
            queryset = queryset.filter(proposal_version_id=proposal_version_id)

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Agreements fetched successfully.",
            data=serializer.data
        )

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        agreement = serializer.save()
        return Responses.success_response(
            status=status.HTTP_201_CREATED,
            message="Agreement created successfully.",
            data=self.get_serializer(agreement).data
        )

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        new_status = request.data.get("status")

        try:
            new_status = int(new_status) if new_status is not None else None
        except (ValueError, TypeError):
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Status must be a valid integer.",
                data={"status": "Status must be a valid integer."}
            )

        valid_statuses = [
            AgreementStatusChoices.ACCEPTED,
            AgreementStatusChoices.REJECTED,
            AgreementStatusChoices.TERMINATED
        ]

        if new_status not in valid_statuses:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Status must be Accepted, Rejected, or Terminated.",
                data={"status": "Invalid status."}
            )

        instance.status = new_status
        if new_status == AgreementStatusChoices.ACCEPTED:
            instance.accepted_by = request.user
        elif new_status == AgreementStatusChoices.REJECTED:
            instance.rejected_by = request.user
        elif new_status == AgreementStatusChoices.TERMINATED:
            instance.terminated_by = request.user

        instance.save(update_fields=[
            "status", "accepted_by", "rejected_by", "terminated_by", "updated_at"
        ])

        serializer = self.get_serializer(instance)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Agreement status updated successfully.",
            data=serializer.data
        )