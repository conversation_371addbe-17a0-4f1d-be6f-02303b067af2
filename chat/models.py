from django.db import models
from campaigns.models import ProposalVersion, Room
from accounts.models import User,BaseModel
from decimal import Decimal

from talent_trade.utils.choices import AgreementStatusChoices


class RoomMessage(BaseModel):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='messages')
    message = models.CharField(max_length=500)
    message_type = models.CharField(max_length=50, default='text')
    sender = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='sent_messages')
    is_read_by = models.CharField(max_length=10, choices=[('talent', 'Talent'), ('brand', 'Brand')], null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['created_at'], name='roommessage_createdat_idx'),
        ]
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender} in {self.room} at {self.created_at}"

# class AgreementStatus(models.TextChoices):
#     SENT = 'SENT', 'Sent'
#     ACCEPTED = 'ACCEPTED', 'Accepted'
#     REJECTED = 'REJECTED', 'Rejected'
#     TERMINATED = 'TERMINATED', 'Terminated'

class Agreement(BaseModel):
    proposal_version = models.ForeignKey(
            ProposalVersion, 
            on_delete=models.CASCADE, 
            related_name='agreements',
            null=True,
            blank=True
        )    
    file = models.FileField(upload_to='agreements/',null=True,blank=True)
    content = models.TextField(null=True,blank=True)
    feedback = models.TextField(null=True,blank=True)
    status = models.IntegerField(choices=AgreementStatusChoices.choices, default=AgreementStatusChoices.SENT)
    sent_by = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='sent_agreements')
    sent_to = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='received_agreements')
    version = models.DecimalField(max_digits=4, decimal_places=1, default=Decimal("1.0"))
    accepted_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, related_name="accepted_agreements", null=True, blank=True)
    rejected_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, related_name="rejected_agreements", null=True, blank=True)
    terminated_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, related_name="terminated_agreements", null=True, blank=True)
    room = models.ForeignKey(Room,on_delete=models.CASCADE,null=True, blank=True)



    class Meta:
        ordering = ['-version', '-created_at']
        # indexes = [
        #     models.Index(fields=['proposal', '-version']),
        #     models.Index(fields=['sent_by', '-created_at']),
        #     models.Index(fields=['sent_to', '-created_at']),
        #     models.Index(fields=['status']),
        # ]
        # unique_together = ['proposal', 'version']
