from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async


class TokenAuthMiddleware(BaseMiddleware):
    async def __call__(self, scope, receive, send):
        # Get the token from query parameters
        query_string = scope.get('query_string', b'').decode()
        query_params = dict(x.split('=') for x in query_string.split('&') if x)
        token_key = query_params.get('token', None)
        
        if token_key:
            scope['user'] = await self.get_user(token_key)
        else:
            # Import here to avoid circular imports
            from django.contrib.auth.models import AnonymousUser
            scope['user'] = AnonymousUser()
        
        return await super().__call__(scope, receive, send)

    @database_sync_to_async
    def get_user(self, token_key):
        try:
            # Import here to avoid circular imports
            from accounts.models import Token
            from django.contrib.auth.models import AnonymousUser
            
            token = Token.objects.select_related('user').get(key=token_key)
            return token.user
        except Exception:
            return AnonymousUser() 