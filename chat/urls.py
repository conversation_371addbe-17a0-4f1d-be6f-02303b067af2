from django.urls import path
from . import views
from .views import AgreementAPIView 

urlpatterns = [
    path('rooms/<int:room_id>/messages/', views.get_room_messages, name='get_room_messages'),
    path('rooms/<int:room_id>/messages/detailed/', views.get_detailed_room_messages, name='get_detailed_room_messages'),
    path('rooms/<int:room_id>/messages/save/', views.save_message, name='save_message'),
    path('rooms/<int:room_id>/messages/mark-read/', views.mark_messages_as_read, name='mark_messages_as_read'),
    path('messages/<int:message_id>/delete/', views.delete_message, name='delete_message'),
    path('user/rooms/', views.get_user_rooms_with_messages, name='get_user_rooms_with_messages'),

    # Agreement endpoints (class-based, explicit)
    path('agreements/', AgreementAPIView.as_view({'post': 'create', 'get': 'list'}), name='agreement'),
    path('agreements/<int:id>/', AgreementAPIView.as_view({'get': 'retrieve', 'patch': 'partial_update'}), name='agreement-detail'),
    # path('agreements/upload/', AgreementViewSet.as_view({'post': 'create'}), name='upload_agreement'),
    # path('agreements/', AgreementViewSet.as_view({'get': 'list'}), name='list_agreements'),
    # path('agreements/<int:id>/status/', AgreementViewSet.as_view({'patch': 'partial_update', 'put': 'partial_update'}), name='update_agreement_status'),
] 