# Generated by Django 5.2.1 on 2025-06-13 01:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0018_notification_token'),
        ('campaigns', '0009_campaignbookmark'),
    ]

    operations = [
        migrations.AddField(
            model_name='campaign',
            name='campaign_banner',
            field=models.ImageField(blank=True, null=True, upload_to='campaign_banners/'),
        ),
        migrations.AddField(
            model_name='campaign',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='campaigns_expertise', to='accounts.expertisearea'),
        ),
        migrations.AddField(
            model_name='campaign',
            name='preferred_gender',
            field=models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], default='O', max_length=10),
        ),
    ]
