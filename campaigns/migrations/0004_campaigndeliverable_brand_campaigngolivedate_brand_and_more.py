# Generated by Django 5.2.1 on 2025-06-10 06:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0003_alter_talentcampaignapplication_status_proposal_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='campaigndeliverable',
            name='brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='campaign_deliverables', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='campaigngolivedate',
            name='brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='campaign_go_live_dates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='campaignplatformselection',
            name='brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='campaign_platform_selections', to=settings.AUTH_USER_MODEL),
        ),
    ]
