# Generated by Django 5.2.1 on 2025-06-12 16:21

import django.core.validators
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0007_rename_initial_offer_proposal_initial_offer_max_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='proposalversion',
            name='city',
        ),
        migrations.RemoveField(
            model_name='proposalversion',
            name='state',
        ),
        migrations.AddField(
            model_name='proposalversion',
            name='offer_amount_max',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
        ),
        migrations.AddField(
            model_name='proposalversion',
            name='offer_amount_min',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='proposalversion',
            name='offer_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
        ),
    ]
