# Generated by Django 5.2.1 on 2025-07-01 07:19

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0020_script'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='script',
            name='status',
            field=models.IntegerField(choices=[(0, 'Draft'), (1, 'Sent'), (2, 'Finalised'), (3, 'Terminate'), (4, 'Rejected')], default=1),
        ),
        migrations.CreateModel(
            name='ContentUpload',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('upload_content', models.J<PERSON><PERSON>ield(blank=True, default=list, help_text='List of up to 4 image/video URLs or file paths', null=True)),
                ('link', models.URLField(blank=True, help_text='External link to content if applicable', null=True)),
                ('version', models.DecimalField(decimal_places=1, default=Decimal('1.0'), max_digits=4)),
                ('status', models.IntegerField(choices=[(1, 'Sent'), (2, 'Finalised'), (3, 'Terminate'), (4, 'Rejected')], default=1)),
                ('feedback', models.TextField(blank=True, help_text='Feedback from brand for rejected content', null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_content_uploads', to=settings.AUTH_USER_MODEL)),
                ('deliverable', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_uploads', to='campaigns.proposalversiondeliverableitem')),
                ('rejected_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rejected_content_uploads', to=settings.AUTH_USER_MODEL)),
                ('room', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='campaigns.room')),
                ('script', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_uploads', to='campaigns.script')),
                ('sent_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sent_content_uploads', to=settings.AUTH_USER_MODEL)),
                ('sent_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_content_uploads', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Content Upload',
                'verbose_name_plural': 'Content Uploads',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['script', '-version'], name='campaigns_c_script__f0a4f1_idx'), models.Index(fields=['deliverable', '-version'], name='campaigns_c_deliver_322208_idx'), models.Index(fields=['status'], name='campaigns_c_status_2f4041_idx'), models.Index(fields=['sent_by', '-created_at'], name='campaigns_c_sent_by_c7489c_idx'), models.Index(fields=['sent_to', '-created_at'], name='campaigns_c_sent_to_90f369_idx')],
            },
        ),
    ]
