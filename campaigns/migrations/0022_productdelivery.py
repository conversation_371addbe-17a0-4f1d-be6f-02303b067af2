# Generated by Django 5.2.1 on 2025-07-03 17:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0021_alter_script_status_contentupload'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductDelivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('delivery_address', models.TextField(blank=True, null=True)),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('receipt_status', models.CharField(choices=[('pending', 'Pending'), ('received', 'Received'), ('not_received', 'Not Received')], default='pending', max_length=20)),
                ('last_receipt_check', models.DateTimeField(blank=True, null=True)),
                ('next_check_allowed', models.DateTimeField(blank=True, null=True)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_deliveries_sent', to=settings.AUTH_USER_MODEL)),
                ('proposal_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_deliveries', to='campaigns.proposalversion')),
                ('talent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_deliveries_received', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('proposal_version', 'talent')},
            },
        ),
    ]
