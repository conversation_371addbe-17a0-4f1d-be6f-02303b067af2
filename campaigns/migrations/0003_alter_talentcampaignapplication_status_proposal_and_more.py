# Generated by Django 5.2.1 on 2025-06-08 17:05

import accounts.choices
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0014_alter_bankaccountdetails_aadhar_number_and_more'),
        ('campaigns', '0002_talentcampaignapplication'),
    ]

    operations = [
        migrations.AlterField(
            model_name='talentcampaignapplication',
            name='status',
            field=models.IntegerField(choices=[(1, 'Applied'), (2, 'Shortlisted'), (3, 'Selected'), (4, 'Rejected')], default=1),
        ),
        migrations.CreateModel(
            name='Proposal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('duration', models.PositiveIntegerField()),
                ('duration_type', models.CharField(choices=[('D', 'Days'), ('W', 'Weeks'), ('M', 'Months')], default=accounts.choices.DurationTypeChoices['MONTHS'], max_length=10)),
                ('duration_is_negotiable', models.BooleanField(default=False)),
                ('state', models.CharField(db_index=True, max_length=100)),
                ('city', models.CharField(db_index=True, max_length=100)),
                ('location_is_negotiable', models.BooleanField(default=False)),
                ('initial_offer', models.DecimalField(decimal_places=2, max_digits=10)),
                ('offer_is_negotiable', models.BooleanField(default=False)),
                ('campaign', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='campaigns.campaign')),
                ('categories', models.ManyToManyField(related_name='proposals', to='accounts.expertisearea')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProposalDeliverable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('proposal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliverables', to='campaigns.proposal')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProposalDeliverableItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.IntegerField()),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.platformcategory')),
                ('deliverable', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='campaigns.proposaldeliverable')),
                ('platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.socialmediaplatform')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProposalPlatform',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_negotiable', models.BooleanField(default=False)),
                ('platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.socialmediaplatform')),
                ('proposal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platforms', to='campaigns.proposal')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
