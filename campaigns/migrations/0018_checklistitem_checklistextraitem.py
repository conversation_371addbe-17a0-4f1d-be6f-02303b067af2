# Generated by Django 5.2.1 on 2025-06-29 17:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0017_campaign_campaign_type_campaignproposalproduct_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChecklistItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('outfit', models.CharField(blank=True, max_length=255, null=True)),
                ('background', models.CharField(blank=True, max_length=255, null=True)),
                ('sound_quality', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('lighting', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('camera_orientation', models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('editing', models.CharField(blank=True, max_length=255, null=True)),
                ('script_breakdown', models.CharField(blank=True, max_length=255, null=True)),
                ('deliverable', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checklist', to='campaigns.proposalversiondeliverable')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ChecklistExtraItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('text', models.TextField(blank=True, help_text='Value entered by talent', null=True)),
                ('checklist_media', models.ImageField(blank=True, null=True, upload_to='checklist_media/')),
                ('checklist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='extra_checklist', to='campaigns.checklistitem')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
