# Generated by Django 5.2.1 on 2025-06-25 04:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0016_alter_campaign_hashtags'),
    ]

    operations = [
        migrations.AddField(
            model_name='campaign',
            name='campaign_type',
            field=models.CharField(choices=[('paid', 'Paid'), ('barter', 'Barter')], default='paid', max_length=10),
        ),
        migrations.CreateModel(
            name='CampaignProposalProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Product name', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Product description', max_length=1000, null=True)),
                ('quantity_available', models.PositiveIntegerField(default=1, help_text='Number of products available for this campaign')),
                ('image', models.ImageField(blank=True, null=True, upload_to='barter_product_images/')),
                ('is_non_negotiable', models.BooleanField(default=False, help_text='Mark if the product price/availability is non-negotiable')),
                ('proposal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='proposal_products', to='campaigns.proposal')),
            ],
            options={
                'verbose_name': 'Campaign Product',
                'verbose_name_plural': 'Campaign Products',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CampaignProposalVersionProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Product name', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Product description', max_length=1000, null=True)),
                ('quantity_available', models.PositiveIntegerField(default=1, help_text='Number of products available for this campaign')),
                ('image', models.ImageField(blank=True, null=True, upload_to='barter_product_images/')),
                ('is_non_negotiable', models.BooleanField(default=False, help_text='Mark if the product price/availability is non-negotiable')),
                ('proposal_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='barter_products', to='campaigns.proposalversion')),
            ],
            options={
                'verbose_name': 'Campaign Product',
                'verbose_name_plural': 'Campaign Products',
                'ordering': ['-created_at'],
            },
        ),
    ]
