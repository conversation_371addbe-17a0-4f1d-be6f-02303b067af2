# Generated by Django 5.2.1 on 2025-06-29 17:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0018_checklistitem_checklistextraitem'),
    ]

    operations = [
        migrations.CreateModel(
            name='CampaignProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Product name', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Product description', max_length=1000, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='campaign_product_images/')),
                ('quantity_available', models.PositiveIntegerField(default=1, help_text='Number of products available for this campaign')),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='campaign_products', to='campaigns.campaign')),
            ],
            options={
                'verbose_name': 'Campaign Product',
                'verbose_name_plural': 'Campaign Products',
                'ordering': ['-created_at'],
            },
        ),
    ]
