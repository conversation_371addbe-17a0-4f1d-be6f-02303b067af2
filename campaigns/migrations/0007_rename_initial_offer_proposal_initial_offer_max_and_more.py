# Generated by Django 5.2.1 on 2025-06-12 11:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0006_alter_proposal_duration_type'),
    ]

    operations = [
        migrations.RenameField(
            model_name='proposal',
            old_name='initial_offer',
            new_name='initial_offer_max',
        ),
        migrations.RemoveField(
            model_name='proposal',
            name='city',
        ),
        migrations.RemoveField(
            model_name='proposal',
            name='state',
        ),
        migrations.AddField(
            model_name='proposal',
            name='initial_offer_min',
            field=models.DecimalField(decimal_places=2, default=10.0, max_digits=10),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='talentcampaignapplication',
            name='status',
            field=models.IntegerField(choices=[(1, 'Applied'), (2, 'Active'), (3, 'Accepted'), (4, 'Rejected'), (5, 'Unapplied')], default=1),
        ),
    ]
