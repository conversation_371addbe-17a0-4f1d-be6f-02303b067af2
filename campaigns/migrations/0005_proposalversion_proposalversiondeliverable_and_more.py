# Generated by Django 5.2.1 on 2025-06-10 16:25

import accounts.choices
import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0016_socialconnects_access_token_and_more'),
        ('campaigns', '0004_campaigndeliverable_brand_campaigngolivedate_brand_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProposalVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.FloatField(help_text='Version number (e.g., 1.0, 1.1, 1.2)')),
                ('status', models.CharField(choices=[('SENT', 'Sent'), ('ACCEPTED', 'Accepted'), ('TERMINATED', 'Terminated')], default='SENT', max_length=20)),
                ('duration', models.PositiveIntegerField()),
                ('duration_type', models.CharField(choices=[('D', 'Days'), ('W', 'Weeks'), ('M', 'Months')], default=accounts.choices.DurationTypeChoices['MONTHS'], max_length=10)),
                ('state', models.CharField(db_index=True, max_length=100)),
                ('city', models.CharField(db_index=True, max_length=100)),
                ('offer_amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('comments', models.TextField(blank=True, help_text='Comments or notes for this version', null=True)),
                ('proposal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='campaigns.proposal')),
                ('sent_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_proposals', to=settings.AUTH_USER_MODEL)),
                ('sent_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_proposals', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-version'],
            },
        ),
        migrations.CreateModel(
            name='ProposalVersionDeliverable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('is_negotiable', models.BooleanField(default=False)),
                ('proposal_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliverables', to='campaigns.proposalversion')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProposalVersionDeliverableItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('is_negotiable', models.BooleanField(default=False)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.platformcategory')),
                ('deliverable', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='campaigns.proposalversiondeliverable')),
                ('platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.socialmediaplatform')),
            ],
        ),
        migrations.CreateModel(
            name='ProposalVersionPlatform',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_negotiable', models.BooleanField(default=False)),
                ('platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.socialmediaplatform')),
                ('proposal_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platforms', to='campaigns.proposalversion')),
            ],
        ),
        migrations.AddIndex(
            model_name='proposalversion',
            index=models.Index(fields=['proposal', '-version'], name='campaigns_p_proposa_8ab274_idx'),
        ),
        migrations.AddIndex(
            model_name='proposalversion',
            index=models.Index(fields=['sent_by', '-created_at'], name='campaigns_p_sent_by_9f4080_idx'),
        ),
        migrations.AddIndex(
            model_name='proposalversion',
            index=models.Index(fields=['sent_to', '-created_at'], name='campaigns_p_sent_to_d7f63e_idx'),
        ),
        migrations.AddIndex(
            model_name='proposalversion',
            index=models.Index(fields=['status'], name='campaigns_p_status_ef171f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='proposalversion',
            unique_together={('proposal', 'version')},
        ),
        migrations.AlterUniqueTogether(
            name='proposalversiondeliverableitem',
            unique_together={('deliverable', 'platform', 'category')},
        ),
        migrations.AlterUniqueTogether(
            name='proposalversionplatform',
            unique_together={('proposal_version', 'platform')},
        ),
    ]
