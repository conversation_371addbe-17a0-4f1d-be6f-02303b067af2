# Generated by Django 5.2.1 on 2025-06-16 05:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0011_remove_campaign_category_campaign_category'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('last_message_at', models.DateTimeField(blank=True, null=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rooms', to='campaigns.campaign')),
            ],
            options={
                'verbose_name': 'Room',
                'verbose_name_plural': 'Rooms',
            },
        ),
        migrations.CreateModel(
            name='RoomParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('role', models.CharField(choices=[('brand', 'Brand'), ('talent', 'Talent')], max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('last_read_at', models.DateTimeField(blank=True, null=True)),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='campaigns.room')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_participations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Room Participant',
                'verbose_name_plural': 'Room Participants',
            },
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['campaign'], name='campaigns_r_campaig_dc6519_idx'),
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['is_active'], name='campaigns_r_is_acti_864682_idx'),
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['last_message_at'], name='campaigns_r_last_me_95ad37_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['room'], name='campaigns_r_room_id_75be8c_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['user'], name='campaigns_r_user_id_e572c1_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['role'], name='campaigns_r_role_c12006_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['is_active'], name='campaigns_r_is_acti_9da424_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='roomparticipant',
            unique_together={('room', 'user')},
        ),
    ]
