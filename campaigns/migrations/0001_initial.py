# Generated by Django 5.2.1 on 2025-06-04 09:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0012_socialconnects_connected_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Campaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign_name', models.CharField(max_length=255)),
                ('brief', models.TextField(max_length=3000, null=True)),
                ('hashtags', models.CharField(help_text='Comma-separated hashtags', max_length=512)),
                ('final_submission_date', models.DateField()),
                ('preferred_state', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('preferred_city', models.CharField(blank=True, max_length=100, null=True)),
                ('preferred_languages', models.JSONField(blank=True, default=list, null=True)),
                ('min_budget', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('max_budget', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('status', models.IntegerField(choices=[(1, 'Draft'), (2, 'Active'), (3, 'Archived')], default=1)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='campaigns', to=settings.AUTH_USER_MODEL)),
                ('languages', models.ManyToManyField(blank=True, related_name='campaign_language', to='accounts.language')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CampaignDeliverable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('count', models.PositiveIntegerField(default=1)),
                ('category_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='campaign_deliverables', to='accounts.platformcategory')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CampaignGoLiveDate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('go_live_date', models.DateField()),
                ('is_active', models.BooleanField(default=False)),
                ('deliverable', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='go_live_dates', to='campaigns.campaigndeliverable')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CampaignPlatformSelection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platform_selections', to='campaigns.campaign')),
                ('platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='campaign_platforms', to='accounts.socialmediaplatform')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='campaigndeliverable',
            name='platform_selection',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliverables', to='campaigns.campaignplatformselection'),
        ),
    ]
