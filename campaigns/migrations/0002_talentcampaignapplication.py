# Generated by Django 5.2.1 on 2025-06-05 10:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0013_user_kyc_status_panverification_aadhaarclient'),
        ('campaigns', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TalentCampaignApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.IntegerField(choices=[(0, 'Applied'), (1, 'Shortlisted'), (2, 'Selected'), (3, 'Rejected')], default=0)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='campaigns.campaign')),
                ('talent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='accounts.talentprofile')),
            ],
            options={
                'unique_together': {('talent', 'campaign')},
            },
        ),
    ]
