from django.utils import timezone
from django.db import transaction
from .models import ContentUpload
from talent_trade.utils.choices import ContentUploadStatus


class ContentUploadService:
    """
    Service class for managing content upload workflow
    """
    
    @staticmethod
    def create_content_upload(script, deliverable, upload_content, sent_by, sent_to, room=None, link=None):
        """
        Create a new content upload by talent
        
        Args:
            script: Script instance
            deliverable: ProposalVersionDeliverableItem instance
            upload_content: List of content URLs/paths (max 4)
            sent_by: User who is uploading (talent)
            sent_to: User who will review (brand)
            room: Optional room for communication
            link: Optional external link
            
        Returns:
            ContentUpload instance
        """
        if len(upload_content) > 4:
            raise ValueError("Maximum 4 content items allowed")
            
        return ContentUpload.objects.create(
            script=script,
            deliverable=deliverable,
            upload_content=upload_content,
            sent_by=sent_by,
            sent_to=sent_to,
            room=room,
            link=link
        )
    
    @staticmethod
    def approve_content(content_upload, approved_by, feedback=None):
        """
        Approve content upload by brand
        
        Args:
            content_upload: ContentUpload instance
            approved_by: User who is approving (brand)
            feedback: Optional feedback
            
        Returns:
            Updated ContentUpload instance
        """
        with transaction.atomic():
            content_upload.status = ContentUploadStatus.FINALISED
            content_upload.approved_by = approved_by
            if feedback:
                content_upload.feedback = feedback
            content_upload.save()
            
        return content_upload
    
    @staticmethod
    def reject_content(content_upload, rejected_by, feedback):
        """
        Reject content upload by brand
        
        Args:
            content_upload: ContentUpload instance
            rejected_by: User who is rejecting (brand)
            feedback: Required feedback for rejection
            
        Returns:
            Updated ContentUpload instance
        """
        if not feedback:
            raise ValueError("Feedback is required when rejecting content")
            
        with transaction.atomic():
            content_upload.status = ContentUploadStatus.REJECTED
            content_upload.rejected_by = rejected_by
            content_upload.feedback = feedback
            content_upload.save()
            
        return content_upload
    

    
    @staticmethod
    def get_content_history(deliverable, script):
        """
        Get all content uploads for a specific deliverable and script
        
        Args:
            deliverable: ProposalVersionDeliverableItem instance
            script: Script instance
            
        Returns:
            QuerySet of ContentUpload instances ordered by version
        """
        return ContentUpload.objects.filter(
            deliverable=deliverable,
            script=script
        ).order_by('version')
    
    @staticmethod
    def get_latest_content(deliverable, script):
        """
        Get the latest content upload for a specific deliverable and script
        
        Args:
            deliverable: ProposalVersionDeliverableItem instance
            script: Script instance
            
        Returns:
            Latest ContentUpload instance or None
        """
        return ContentUpload.objects.filter(
            deliverable=deliverable,
            script=script
        ).order_by('-version').first()
    
    @staticmethod
    def get_pending_content_for_brand(brand_user):
        """
        Get all pending content uploads for a brand to review
        
        Args:
            brand_user: User instance (brand)
            
        Returns:
            QuerySet of ContentUpload instances with SENT status
        """
        return ContentUpload.objects.filter(
            sent_to=brand_user,
            status=ContentUploadStatus.SENT
        ).order_by('-created_at')
    
    @staticmethod
    def get_content_by_talent(talent_user):
        """
        Get all content uploads by a talent
        
        Args:
            talent_user: User instance (talent)
            
        Returns:
            QuerySet of ContentUpload instances
        """
        return ContentUpload.objects.filter(
            sent_by=talent_user
        ).order_by('-created_at') 