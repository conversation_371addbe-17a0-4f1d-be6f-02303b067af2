import nested_admin
from django.contrib import admin

from campaigns.models import Campaign, CampaignDeliverable, CampaignGoLiveDate, CampaignPlatformSelection, TalentCampaignApplication


from talent_trade.utils.choices import CampaignStatusChoices

class CampaignGoLiveDateInline(nested_admin.NestedStackedInline):
    model = CampaignGoLiveDate
    extra = 0
    fields = ('go_live_date', 'is_active')

# Inline for Deliverable
class CampaignDeliverableInline(nested_admin.NestedStackedInline):
    model = CampaignDeliverable
    extra = 0
    fields = ('category_type', 'count')
    inlines = [CampaignGoLiveDateInline]

# Inline for Platform Selection
class CampaignPlatformSelectionInline(nested_admin.NestedStackedInline):
    model = CampaignPlatformSelection
    extra = 0
    fields = ('platform', )
    inlines = [CampaignDeliverableInline]



class StatusFilter(admin.SimpleListFilter):
    title = 'By status'
    parameter_name = 'status'

    # your DB values are saved as int
    status_mapping = {
        'Draft': 1,
        'Active': 2,
        'Archived': 3
    }

    def lookups(self, request, model_admin):
        return [
            ('Draft', 'Draft'),
            ('Active', 'Active'),
            ('Archived', 'Archived'),
        ]

    def queryset(self, request, queryset):
        if self.value():
            status_value = self.status_mapping.get(self.value())
            return queryset.filter(status=status_value)
        return queryset


class PreferredStateFilter(admin.SimpleListFilter):
    title = 'By preferred state'
    parameter_name = 'preferred_state'

    def lookups(self, request, model_admin):
        states = Campaign.objects.values_list('preferred_state', flat=True).distinct()
        return [(state, state) for state in states if state]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(preferred_state=self.value())
        return queryset


class PreferredCityFilter(admin.SimpleListFilter):
    title = 'By preferred city'
    parameter_name = 'preferred_city'

    def lookups(self, request, model_admin):
        cities = Campaign.objects.values_list('preferred_city', flat=True).distinct()
        return [(city, city) for city in cities if city]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(preferred_city=self.value())
        return queryset


@admin.register(Campaign)
class CampaignAdmin(nested_admin.NestedModelAdmin):
    list_display = ('campaign_name', 'brand', 'final_submission_date', 'min_budget', 'max_budget', 'status')
    search_fields = ('campaign_name', 'brand__email')
    list_filter = (StatusFilter, PreferredStateFilter, PreferredCityFilter)  
    list_filter_sidebar = True  
    readonly_fields = ('id', 'created_at', 'updated_at')
    exclude = ('preferred_languages',)

    fieldsets = (
        ('Basic Info', {
            'fields': ('campaign_name', 'brand', 'brief', 'hashtags', 'campaign_banner', 'status','campaign_type')
        }),
        ('Preferences', {
            'fields': ('preferred_state', 'preferred_city', 'preferred_gender', 'languages', 'category')
        }),
        ('Budget Info', {
            'fields': ('min_budget', 'max_budget')
        }),
        ('Important Dates', {
            'fields': ('final_submission_date',)
        }),
        ('Meta Info', {
            'fields': ('id', 'created_at', 'updated_at')
        }),
    )

    inlines = [CampaignPlatformSelectionInline]

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['custom_buttons'] = [
            {'title': 'All Campaigns', 'url': '.'},
            {'title': 'Active Campaigns', 'url': '?status=2'},
            {'title': 'Draft Campaigns', 'url': '?status=1'},
            {'title': 'Archived Campaigns', 'url': '?status=3'},
        ]
        return super().changelist_view(request, extra_context=extra_context)

    # override template
    change_list_template = "admin/campaigns/campaign/change_list.html"

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('brand')

@admin.register(TalentCampaignApplication)
class TalentCampaignApplicationAdmin(admin.ModelAdmin):
    list_display = ('id', 'get_talent_name', 'get_campaign_name', 'status')
    search_fields = ('talent__name', 'campaign__campaign_name')
    list_filter = ('status', )

    def get_talent_name(self, obj):
        return obj.talent.name
    get_talent_name.short_description = 'Talent Name'

    def get_campaign_name(self, obj):
        return obj.campaign.campaign_name
    get_campaign_name.short_description = 'Campaign Name'