from django.urls import path
from .views import *

urlpatterns = [
    path("campaign-url/", CampaignStepViewSet.as_view(), name="campaigns"),
    path(
        "campaign-url/<int:campaign_id>/",
        CampaignStepViewSet.as_view(),
        name="campaign-detail",
    ),
    path(
        "campaign-application/",
        TalentCampaignApplicationViewSet.as_view({"post": "create", "get": "list"}),
        name="campaign_application",
    ),
    path(
        "campaign-applicants/<int:campaign_id>/",
        CampaignApplicantsView.as_view(),
        name="campaign-applicants",
    ),
    path(
        "proposal/",
        ProposalView.as_view({"post": "create", "get": "list"}),
        name="proposal-create",
    ),
    path(
        "proposal/<int:id>/",
        ProposalView.as_view({"patch": "partial_update", "get": "retrieve"}),
        name="proposal-update",
    ),
    path(
        "proposal-version/",
        ProposalVersionViewSet.as_view({"post": "create", "get": "list"}),
        name="proposal-version-create",
    ),
    path(
        "proposal-version/<int:id>/",
        ProposalVersionViewSet.as_view({"patch": "update", "get": "retrieve"}),
        name="proposal-version-detail",
    ),
    path(
        "campaign-unapply/<int:campaign_id>/",
        TalentCampaignApplicationViewSet.as_view({"delete":"delete"}),
        name="campaign_unapply"
    ),
    path(
        "campaign-bookmark/<int:campaign_id>/",
        CampaignBookmarkViewSet.as_view({"post": "create", "delete": "destroy"}),
        name="campaign_bookmark",
    ),
    path(
        "bookmark-campaign-list/",
        CampaignBookmarkViewSet.as_view({'get':'list'}),
        name='bookmark_campaign_list'
    ),
    path('rooms/', RoomView.as_view(), name='rooms'),
    path('rooms/<int:campaign_id>/', RoomView.as_view(), name='room-detail'),
    path('greeting-messages/', GreetingMessageView.as_view(), name='greeting-messages'),
    path('active-campaign-proposals/<int:talent_id>/', ActiveCampaignProposalsAPIView.as_view(), name='active-campaign-proposals'),
    
    # Checklist endpoints - handles both ChecklistItem and ChecklistExtraItem
    path(
        "checklist/",
        ChecklistItemViewSet.as_view({"post": "create", "get": "list"}),
        name="checklist-create-list",
    ),
    path(
        "checklist/<int:id>/",
        ChecklistItemViewSet.as_view({"get": "retrieve", "patch": "partial_update", "put": "update", "delete": "destroy"}),
        name="checklist-detail",
    ),
    path('scripts/', ScriptAPIView.as_view({'post':'create','get':'list'}), name='script'),
    path('scripts/<int:id>/', ScriptAPIView.as_view({'get':'retrieve','patch':'partial_update'}), name='script-detail'),

    # Content Upload endpoints
    path(
        "content-uploads/",
        ContentUploadViewSet.as_view({"post": "create", "get": "list"}),
        name="content-upload-create-list",
    ),
    path(
        "content-uploads/<int:id>/",
        ContentUploadViewSet.as_view({"get": "retrieve", "patch": "partial_update"}),
        name="content-upload-detail",
    ),

    path(
        "content-uploads/history/<int:script_id>/<int:deliverable_id>/",
        ContentUploadHistoryView.as_view(),
        name="content-upload-history",
    ),

    # Product Delivery endpoints
    path(
        "product-deliveries/",
        ProductDeliveryViewSet.as_view({"get": "list", "post": "create"}),
        name="product-delivery-list",
    ),
    path(
        "product-deliveries/<int:id>/",
        ProductDeliveryViewSet.as_view({"get": "retrieve", "put": "update", "patch": "partial_update"}),
        name="product-delivery-detail",
    ),
    path(
        "campaign-reviews/",
        CampaignReviewViewSet.as_view({"post": "create", "get": "list"}),
        name="campaign-reviews",
    ),
    path(
        "campaign-reviews/<int:id>/",
        CampaignReviewViewSet.as_view({"get": "retrieve"}),
        name="campaign-review-detail",
    ),

    # Campaign Types endpoint
    path(
        "campaign-types/",
        CampaignTypeAPIView.as_view(),
        name="campaign-types",
    ),

]
