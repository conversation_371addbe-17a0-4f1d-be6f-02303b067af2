# Standard Library Imports
from django.db import transaction

from django.shortcuts import get_object_or_404
from rest_framework import serializers, status
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from rest_framework import serializers
from django.db.models import Q
from campaigns.filters import TalentCampaignApplicationFilter
from campaigns.helpers import create_proposal_versions_and_applications
from campaigns.pagination import CustomPagination
from .models import *
from .serializers import *
from talent_trade.utils.choices import CampaignStatusChoices, ApplicationStatusChoices, CampaignTypeChoices
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework.exceptions import NotFound, ValidationError
from datetime import date
# Third-party / Project-specific Imports
from accounts.responses import Responses

# Local App Imports
from .models import (
    Campaign,
    CampaignPlatformSelection,
    CampaignDeliverable,
    CampaignGoLiveDate,
    TalentCampaignApplication
)
from .serializers import (
    CampaignApplicantTalentSerializer,
    CampaignRetrieveSerializer,
    CampaignBasicDetailsSerializer,
    CampaignLanguagesSerializer,
    CampaignBudgetSerializer,
    CampaignDeliverableSerializer,
    GoLiveDateSerializer,
    TalentCampaignApplicationSerializer
)

from accounts.models import TalentProfile, BrandProfile
from django.db.models import Q
import logging
logger = logging.getLogger(__name__)
class CampaignStepViewSet(APIView):
    """
    ViewSet for handling step-wise campaign updates
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, campaign_id=None):
        try:
            # Get query parameters
            status_filter = request.query_params.get('status')
            view_type = request.query_params.get('view_type','brand')  
            state_filter = request.query_params.get('state')
            city_filter = request.query_params.get('city')
            gender_filter = request.query_params.get('gender')
            category_filter = request.query_params.get('category')
            brand_filter = request.query_params.get('brand')
            min_price = request.query_params.get('min_price')
            max_price = request.query_params.get('max_price')
            language_filter = request.query_params.get('language') 


            search_query = request.query_params.get('search', None) 

            
            if campaign_id:
                # For specific campaign
                if view_type == 'talent':
                    # Talents can only view active campaigns
                    campaign = Campaign.objects.get(
                        id=campaign_id,
                        status=CampaignStatusChoices.ACTIVE
                    )
                else:
                    # Brands can view their own campaigns
                    campaign = Campaign.objects.get(
                        id=campaign_id,
                        brand=request.user
                    )
                
                serializer = CampaignRetrieveSerializer(campaign,context={'request': request})
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Campaign retrieved successfully',
                    data=serializer.data
                )
            else:
                # For campaign listing
                if view_type == 'talent':
                    # Talents see all active campaigns
                    today = date.today()
                    talent_profile = request.user.talent_profile  # Assuming user is talent

                    # All campaigns which are ACTIVE
                    campaigns = Campaign.objects.filter(status=CampaignStatusChoices.ACTIVE).filter(
                        Q(final_submission_date__gte=today)  # Future submission date
                        |
                        Q(  # OR condition for applied campaigns in past
                            Q(final_submission_date__lt=today) &
                            Q(applications__talent=talent_profile) &
                            ~Q(applications__status=ApplicationStatusChoices.UNAPPLIED)
                        )
                    ).distinct().order_by('-created_at')

                else:
                    # Brands see their own campaigns with optional status filter
                    campaigns = Campaign.objects.filter(brand=request.user).order_by('-created_at')
                if status_filter:
                    try:
                        status_int = int(status_filter)
                        if status_int in [choice[0] for choice in CampaignStatusChoices.choices]:
                            campaigns = campaigns.filter(status=status_int)
                    except ValueError:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message='Invalid status value',
                            data={}
                        )
                
                # Apply location filters if provided
                if state_filter:
                    campaigns = campaigns.filter(preferred_state__iexact=state_filter)
                if city_filter:
                    campaigns = campaigns.filter(preferred_city__iexact=city_filter)

                if gender_filter:
                    try:
                        gender_list = [g.strip() for g in gender_filter.split(',') if g.strip() in [choice[0] for choice in GenderChoices.get_choices()]]
                        if gender_list:
                            campaigns = campaigns.filter(preferred_gender__in=gender_list)
                    except Exception:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message="Invalid gender value(s)",
                            data={}
                        )

                    
                if category_filter:
                    try:
                        # Accept multiple category ids separated by comma
                        category_ids = [int(cid.strip()) for cid in category_filter.split(',') if cid.strip().isdigit()]
                        if category_ids:
                            campaigns = campaigns.filter(category__id__in=category_ids).distinct()
                    except ValueError:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message="Invalid category ID(s)",
                            data={}
                        )
                    
                if brand_filter:
                    try:
                        brand_profile_ids = [int(val.strip()) for val in brand_filter.split(',') if val.strip().isdigit()]
                        if brand_profile_ids:
                            # Get corresponding user IDs from BrandProfile
                            user_ids = BrandProfile.objects.filter(id__in=brand_profile_ids).values_list('user_id', flat=True)
                            campaigns = campaigns.filter(brand_id__in=user_ids)
                    except ValueError:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message="Invalid brand ID(s)",
                            data={}
                        )
                # --- New: Apply min price filter
                if min_price:
                    try:
                        min_price_val = float(min_price)
                        campaigns = campaigns.filter(min_budget__gte=min_price_val)
                    except ValueError:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message="Invalid min_price value",
                            data={}
                        )

                # --- New: Apply max price filter
                if max_price:
                    try:
                        max_price_val = float(max_price)
                        campaigns = campaigns.filter(max_budget__lte=max_price_val)
                    except ValueError:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message="Invalid max_price value",
                            data={}
                        )
                    
                if language_filter:
                    try:
                        language_ids = [int(lang_id.strip()) for lang_id in language_filter.split(',') if lang_id.strip().isdigit()]
                        if language_ids:
                            campaigns = campaigns.filter(languages__id__in=language_ids).distinct()
                    except ValueError:
                        return Responses.error_response(
                            status=status.HTTP_400_BAD_REQUEST,
                            message="Invalid language ID(s)",
                            data={}
                        )

                
                # serializer = CampaignRetrieveSerializer(campaigns, many=True,context={'request': request})
                # return Responses.success_response(
                #     status=status.HTTP_200_OK,
                #     message='Campaigns retrieved successfully',
                #     data=serializer.data
                # )
                    
                # --- Apply Search Filter ---
                if search_query:
                    # Use Q objects for OR conditions across multiple fields
                    campaigns = campaigns.filter(
                        Q(campaign_name__icontains=search_query) |
                        Q(brand__username__icontains=search_query) | 
                        Q(brand__brand_profile__brand_name__icontains=search_query) |
                        Q(hashtags__icontains=search_query) 
                    )

                # Apply pagination
                paginator = CustomPagination()
                paginated_queryset = paginator.paginate_queryset(campaigns, request)
                serializer = CampaignRetrieveSerializer(paginated_queryset, many=True, context={'request': request})
                
                return paginator.get_paginated_response(serializer.data)
                
        except Campaign.DoesNotExist:
            # Return empty data structure for new campaigns
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='No campaign found',
                data={
                    'id': None,
                    'step_1': {},  # Basic details
                    'step_2': {    # Languages
                        'languages': []
                    },
                    'step_3': {    # Budget
                        'budget': None
                    },
                    'step_4': {    # Deliverables
                        'deliverables': []
                    },
                    'step_5': {    # Go live dates
                        'go_live_dates': []
                    },
                    'status': None
                }
            )
    
    def patch(self, request, campaign_id=None):
        try:
            if campaign_id:
                campaign = Campaign.objects.get(id=campaign_id, brand=request.user)
                if campaign.status not in [CampaignStatusChoices.DRAFT, CampaignStatusChoices.ARCHIVED]:
                    return Responses.error_response(
                        status=status.HTTP_403_FORBIDDEN,
                        message='Only draft or archived campaigns can be edited',
                        data={}
                    )
            else:
                # Don't create the campaign immediately
                campaign = None

            step = request.data.get('step')
            # Handle different steps
            if step == '1':
                if not campaign:
                    # For step 1, if no campaign exists, we'll create it in handle_step_1
                    return self.handle_step_1(request, None)
                return self.handle_step_1(request, campaign)
            elif step == '2':
                if not campaign:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Campaign not found',
                        data={}
                    )
                return self.handle_step_2(request, campaign)
            elif step == '3':
                if not campaign:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Campaign not found',
                        data={}
                    )
                return self.handle_step_3(request, campaign)
            elif step == '4':
                if not campaign:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Campaign not found',
                        data={}
                    )
                return self.handle_step_4(request, campaign)
            elif step == '5':
                if not campaign:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Campaign not found',
                        data={}
                    )
                return self.handle_step_5(request, campaign)
            elif step == '6':
                if not campaign:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Campaign not found',
                        data={}
                    )
                return self.handle_step_6(request, campaign)
            else:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Invalid step',
                    data={}
                )
        except Campaign.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Campaign not found',
                data={}
            )

    def handle_step_1(self, request, campaign):
        """Update basic campaign details"""
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 1 skipped',
                data={}
            )

        print("Incoming request data:", request.data)  # Debug print
        
        # if campaign is None:
        #     # Creating new campaign
        #     serializer = CampaignBasicDetailsSerializer(data=request.data)
        #     if serializer.is_valid():
        #         print("Serializer validated data:", serializer.validated_data)  # Debug print
        #         # Create campaign with brand and validated data
        #         campaign = Campaign.objects.create(
        #             brand=request.user,
        #             **serializer.validated_data
        #         )
        #         return Responses.success_response(
        #             status=status.HTTP_200_OK,
        #             message='Campaign created successfully',
        #             data={
        #                 'id': campaign.id,
        #                 **serializer.data
        #             }
        #         )
        if campaign is None:
            serializer = CampaignBasicDetailsSerializer(data=request.data)
            if serializer.is_valid():
                validated_data = serializer.validated_data
                category_data = validated_data.pop('category', [])  # M2M
                campaign = Campaign.objects.create(
                    brand=request.user,
                    **validated_data
                )
                if category_data:
                    campaign.category.set(category_data)
                
                response_serializer = CampaignBasicDetailsSerializer(campaign)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Campaign created successfully',
                    data=response_serializer.data
                )
            
        else:
            # Updating existing campaign
            serializer = CampaignBasicDetailsSerializer(campaign, data=request.data, partial=True)
            if serializer.is_valid():
                validated_data = serializer.validated_data
                category_data = validated_data.pop('category', None)
                serializer.save()
                if category_data is not None:
                    campaign.category.set(category_data)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Campaign updated successfully',
                    data=serializer.data
                )
        # else:
        #     # Updating existing campaign
        #     serializer = CampaignBasicDetailsSerializer(campaign, data=request.data, partial=True)
        #     if serializer.is_valid():
        #         print("Serializer validated data:", serializer.validated_data)  # Debug print
        #         serializer.save()
        #         return Responses.success_response(
        #             status=status.HTTP_200_OK,
        #             message='Campaign updated successfully',
        #             data=serializer.data
        #         )
        
        return Responses.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message='Validation error',
            data=serializer.errors
        )

    def handle_step_2(self, request, campaign):
        """Update campaign languages"""
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 2 skipped',
                data={}
            )

        serializer = CampaignLanguagesSerializer(campaign, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 2 completed successfully',
                data=serializer.data
            )
        return Responses.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message='Validation error',
            data=serializer.errors
        )

    def handle_step_3(self, request, campaign):
        """Update campaign budget"""
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 3 skipped',
                data={}
            )

        serializer = CampaignBudgetSerializer(campaign, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 3 completed successfully',
                data=serializer.data
            )
        return Responses.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message='Validation error',
            data=serializer.errors
        )

    def handle_step_4(self, request, campaign):
        """Update campaign deliverables"""
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 4 skipped',
                data={}
            )

        deliverables_data = request.data.get('deliverables', [])
        
        try:
            with transaction.atomic():
                # Clear existing deliverables
                CampaignPlatformSelection.objects.filter(campaign=campaign).delete()
                
                # Create new deliverables
                for deliverable in deliverables_data:
                    serializer = CampaignDeliverableSerializer(data=deliverable,context={'request': request})
                    if serializer.is_valid():
                        platform = serializer.validated_data['platform']
                        category = serializer.validated_data['category_type']
                        count = serializer.validated_data['count']
                        brand = self.request.user
                        
                        # Get or create platform selection
                        platform_selection, _ = CampaignPlatformSelection.objects.get_or_create(
                            campaign=campaign,
                            platform=platform,
                            brand = brand
                        )
                        
                        # Create deliverable
                        CampaignDeliverable.objects.create(
                            platform_selection=platform_selection,
                            category_type=category,
                            count=count,
                            brand = brand
                        )
                    else:
                        raise serializers.ValidationError(serializer.errors)
                
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Step 4 completed successfully',
                    data=CampaignRetrieveSerializer(campaign,context={'request': request}).data
                )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Validation error',
                data=str(e)
            )

    def handle_step_5(self, request, campaign):
        """Update campaign go live dates"""
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 5 skipped',
                data={}
            )

        dates_data = request.data.get('go_live_dates', [])
        
        try:
            with transaction.atomic():
                # Clear existing go live dates
                for platform_selection in campaign.platform_selections.all():
                    for deliverable in platform_selection.deliverables.all():
                        deliverable.go_live_dates.all().delete()
                
                has_active_date = False
                # Create new go live dates
                for date_data in dates_data:
                    serializer = GoLiveDateSerializer(
                        data=date_data,
                        context={'campaign': campaign}
                    )
                    if serializer.is_valid():
                        validated_data = serializer.validated_data
                        validated_data['brand'] = campaign.brand
                        CampaignGoLiveDate.objects.create(**validated_data)                        
                        if validated_data.get('is_active'):
                            has_active_date = True
                    else:
                        raise serializers.ValidationError(serializer.errors)
                
                # Update campaign status if any date is active
                if has_active_date:
                    campaign.status = CampaignStatusChoices.ACTIVE
                    campaign.save()
                
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Step 5 completed successfully',
                    data=CampaignRetrieveSerializer(campaign,context={'request': request}).data
                )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Validation error',
                data=str(e)
            )

    def handle_step_6(self, request, campaign):
        """Update campaign products for delivery"""
        is_skipping = request.data.get('skip', False)
        
        if is_skipping:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 6 skipped',
                data={}
            )

        products_data = request.data.get('products', [])
        
        # Validate that we don't exceed 5 products
        if len(products_data) > 5:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Maximum 5 products allowed per campaign',
                data={}
            )

        try:
            # Clear existing products
            campaign.campaign_products.all().delete()
            
            # Create new products
            created_products = []
            for product_data in products_data:
                serializer = CampaignProductSerializer(
                    data=product_data, 
                    context={'campaign': campaign}
                )
                if serializer.is_valid():
                    product = serializer.save(campaign=campaign)
                    created_products.append(serializer.data)
                else:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Validation error in product data',
                        data=serializer.errors
                    )
            
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Step 6 completed successfully',
                data={'products': created_products}
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Error processing products',
                data={'error': str(e)}
            )


class TalentCampaignApplicationViewSet(ModelViewSet):
    serializer_class = TalentCampaignApplicationSerializer
    permission_classes = [IsAuthenticated]

    filterset_class = TalentCampaignApplicationFilter
    search_fields = [
    'campaign__campaign_name',
    'campaign__hashtags',
    'campaign__brand__brand_profile__brand_name'
    ]

    def get_queryset(self):
        user = self.request.user
        if hasattr(user, 'talent_profile'):
            return TalentCampaignApplication.objects.filter(talent=user.talent_profile).exclude(status=ApplicationStatusChoices.UNAPPLIED).order_by('-created_at')
        return TalentCampaignApplication.objects.none()

    def perform_create(self, serializer):
        # Get the campaign from the serializer data
        campaign_id = serializer.validated_data.get('campaign').id
        talent = serializer.validated_data.get('talent')
        
        # Check if campaign exists and is not archived
        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            raise ValidationError("Campaign not found")
        
        # Check if campaign is archived
        if campaign.status == CampaignStatusChoices.ARCHIVED:
            raise ValidationError("Cannot apply to archived campaigns")
        
        # Check if campaign is active
        if campaign.status != CampaignStatusChoices.ACTIVE:
            raise ValidationError("Can only apply to active campaigns")
        
        # Check if talent already has an application for this campaign
        existing_application = TalentCampaignApplication.objects.filter(
            campaign=campaign,
            talent=talent
        ).first()
        
        if existing_application:
            # Check the status of existing application
            if existing_application.status == ApplicationStatusChoices.APPLIED:
                raise ValidationError("You have already applied to this campaign")
            elif existing_application.status == ApplicationStatusChoices.ACCEPTED:
                raise ValidationError("Your application has already been accepted for this campaign")
            elif existing_application.status == ApplicationStatusChoices.REJECTED:
                raise ValidationError("Your application has already been rejected for this campaign")
            elif existing_application.status == ApplicationStatusChoices.UNAPPLIED:
                # If unapplied, update the status to applied
                existing_application.status = ApplicationStatusChoices.APPLIED
                existing_application.save()
                return existing_application
        
        # If no existing application, create new one
        serializer.save()

    def delete(self, request, campaign_id=None):
        user = request.user

        if not hasattr(user, 'talent_profile'):
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Only talent users can unapply."
            )

        try:
            application = TalentCampaignApplication.objects.get(
                campaign_id=campaign_id,
                talent=user.talent_profile,
                status=ApplicationStatusChoices.APPLIED  
            )
            application.status = ApplicationStatusChoices.UNAPPLIED
            application.save()

            return Responses.success_response(
                status=status.HTTP_200_OK,   
                message="Application marked as unapplied successfully."
            )

        except TalentCampaignApplication.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message="No active applied application found for this campaign."
            )


class CampaignApplicantsView(APIView):
    """View to get campaign applicants"""
    
    def get(self, request, campaign_id):
        try:
            # Get status filter from query params
            status_filter = request.query_params.get('status')
            
            # Base query with campaign filter
            applications = TalentCampaignApplication.objects.filter(
                campaign_id=campaign_id
            ).select_related('talent__user')
            
            # Apply status filter if provided
            if status_filter:
                try:
                    status_int = int(status_filter)
                    applications = applications.filter(status=status_int)
                except ValueError:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message='Invalid status value',
                        data={}
                    )
            
            # Get talent profiles for all applicants
            talent_profiles = []
            for app in applications:
                if app.talent:
                    talent_profile = app.talent
                    # Add application status to talent profile
                    talent_profile.application_status = app.status
                    talent_profiles.append(talent_profile)
            
            # Serialize talent profiles
            serializer = CampaignApplicantTalentSerializer(talent_profiles, many=True)
            
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Campaign applicants retrieved successfully',
                data=serializer.data
            )
            
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to retrieve campaign applicants',
                data={'error': str(e)}
            )
        
class ProposalView(ModelViewSet):
    queryset = Proposal.objects.all()
    serializer_class = ProposalSerializer
    permission_classes = [IsAuthenticated]  
    lookup_field = 'id'

    def get_queryset(self):
        return Proposal.objects.filter(campaign__brand=self.request.user)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        self.perform_create(serializer)
        return Responses.success_response(
            status=status.HTTP_201_CREATED,
            message='Proposal created successfully',
            data=serializer.data
        )
    
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Proposal updated successfully',
            data=serializer.data
        )
    
    def retrieve(self, request, *args, **kwargs):
        """Get a single proposal by ID"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Proposal fetched successfully',
            data=serializer.data
        )

    def list(self, request, *args, **kwargs):
        """List all proposals for the authenticated brand"""
        campaign_id = request.query_params.get('campaign')
        queryset = self.get_queryset()
        if campaign_id:
            queryset = queryset.filter(campaign_id=campaign_id)
        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Proposal list fetched successfully',
            data=serializer.data
        )

class ProposalVersionViewSet(ModelViewSet):
    serializer_class = ProposalVersionSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        """
        Return proposal versions where the user is either the sender or receiver
        """
        user = self.request.user
        return ProposalVersion.objects.filter(
            Q(sent_by=user) | Q(sent_to=user)
        ).select_related(
            'proposal', 'sent_by', 'sent_to'
        ).prefetch_related(
            'platforms', 'deliverables', 'deliverables__items'
        )

    def create(self, request, *args, **kwargs):
        
        # First validate the proposal ownership before proceeding
        proposal_id = request.data.get('proposal')
        if not proposal_id:
            return Response(
                {"detail": "Proposal ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            proposal = Proposal.objects.get(id=proposal_id)
        except Proposal.DoesNotExist:
            return Response(
                {"detail": "Proposal not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        user = request.user
        
        # Check if user is the campaign brand
        is_brand = (proposal.campaign.brand == user)

        if is_brand:
            # For brand, validate talent_id
            talent_id = request.data.get('talent_id')
            if not talent_id:
                return Response(
                    {"detail": "talent_id is required for brand proposals"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if talent has applied
            if not proposal.campaign.applications.filter(talent__id=talent_id).exists():
                return Response(
                    {"detail": "Selected talent has not applied to this campaign"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            # For talent, check if they've applied
            if not proposal.campaign.applications.filter(talent__user=user).exists():
                return Response(
                    {"detail": "You can only negotiate proposals for campaigns you have applied to"},
                    status=status.HTTP_403_FORBIDDEN
                )

        # If we get here, the user has permission to create the proposal version
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        proposal_version = serializer.save()
        
        return Responses.success_response(
            status=status.HTTP_201_CREATED,
            message='Proposal version created successfully',
            data=serializer.data
        )

    def list(self, request, *args, **kwargs):
        """List versions for a specific proposal"""
        proposal_id = request.query_params.get('proposal')
        if not proposal_id:
            return Response(
                {"detail": "proposal parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        queryset = self.get_queryset().filter(proposal_id=proposal_id)
        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Proposal versions fetched successfully',
            data=serializer.data
        )

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Proposal version details fetched successfully',
            data=serializer.data
        )

    def update(self, request, *args, **kwargs):
        """Only allow updating status to ACCEPTED or TERMINATED"""
        instance = self.get_object()
        
        # Only allow updating status
        if list(request.data.keys()) != ['status']:
            return Response(
                {"detail": "Only status field can be updated"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate status value
        new_status = request.data['status']
        if new_status not in [ProposalNegotiationStatus.ACCEPTED, ProposalNegotiationStatus.TERMINATED]:
            return Response(
                {"detail": "Status can only be updated to ACCEPTED or TERMINATED"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Only the recipient can update the status
        if instance.sent_to != request.user:
            return Response(
                {"detail": "Only the recipient can update the status"},
                status=status.HTTP_403_FORBIDDEN
            )

        # If already accepted or terminated, don't allow changes
        if instance.status in [ProposalNegotiationStatus.ACCEPTED, ProposalNegotiationStatus.TERMINATED]:
            return Response(
                {"detail": f"Cannot update status - proposal is already {instance.status}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        instance.status = new_status
        instance.save()

        serializer = self.get_serializer(instance)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message=f'Proposal version {new_status.lower()} successfully',
            data=serializer.data
        )
    

class CampaignBookmarkViewSet(ModelViewSet):
    """
    ViewSet to handle bookmarking and unbookmarking of campaigns
    """
    queryset = CampaignBookmark.objects.all()
    serializer_class = CampaignBookmarkSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CampaignBookmark.objects.filter(talent=self.request.user)

    def create(self, request, *args, **kwargs):
        campaign_id = self.kwargs.get('campaign_id')
        user = request.user

        if not campaign_id:
            raise ValidationError({"campaign_id": "Campaign ID is required."})

        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            raise NotFound("Campaign not found.")

        bookmark, created = CampaignBookmark.objects.get_or_create(
            talent=user, campaign=campaign
        )

        if created:
            return Responses.success_response(
                status=status.HTTP_201_CREATED,
                message="Campaign bookmarked successfully."
            )
        else:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message="Campaign already bookmarked."
            )

    def destroy(self, request, *args, **kwargs):
        campaign_id = self.kwargs.get('campaign_id')
        user = request.user

        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            raise NotFound("Campaign not found.")

        deleted, _ = CampaignBookmark.objects.filter(
            talent=user, campaign=campaign
        ).delete()

        if deleted:
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message="Bookmark removed successfully."
            )
        else:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Bookmark does not exist."
            )

class RoomView(APIView):
    """View for managing chat rooms"""
    permission_classes = [IsAuthenticated]

    def get(self, request, campaign_id=None):
        """Get rooms for the authenticated user"""
        if campaign_id:
            try:
                campaign = Campaign.objects.get(id=campaign_id)
                # Get the room for this campaign where user is a participant
                room = Room.objects.get(
                    campaign=campaign,
                    participants__user=request.user,
                    participants__is_active=True
                )
                serializer = RoomSerializer(room)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Room retrieved successfully',
                    data=serializer.data
                )
            except (Campaign.DoesNotExist, Room.DoesNotExist):
                return Responses.error_response(
                    status=status.HTTP_404_NOT_FOUND,
                    message='Room not found',
                    data={}
                )
        else:
            # Get all rooms where user is a participant
            rooms = Room.objects.filter(
                participants__user=request.user,
                participants__is_active=True
            ).order_by('-last_message_at')
            serializer = RoomSerializer(rooms, many=True)
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Rooms retrieved successfully',
                data=serializer.data
            )

    def post(self, request):
        campaign_ids = request.data.get('campaign_id')  # expecting a list
        talent_id = request.data.get('talent_id')       # this is from TalentProfile

        if not campaign_ids or not isinstance(campaign_ids, list) or not talent_id:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='A list of campaign IDs and Talent ID are required',
                data={}
            )

        if request.user.user_type != 'B':
            return Responses.error_response(
                status=status.HTTP_403_FORBIDDEN,
                message='Only brands can create rooms',
                data={}
            )

        try:
            # Get TalentProfile and corresponding user
            talent_profile = TalentProfile.objects.select_related("user").get(id=talent_id)
            talent_user = talent_profile.user
        except TalentProfile.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Talent not found',
                data={}
            )

        created_rooms = []
        valid_campaign_ids = []  # collect valid ones for proposal creation

        for campaign_id in campaign_ids:
            try:
                campaign = Campaign.objects.get(id=campaign_id)
            except Campaign.DoesNotExist:
                continue  # Skip invalid campaign IDs

            room_exists = Room.objects.filter(
                campaign=campaign,
                participants__user=request.user,
                participants__role='brand',
                participants__room__participants__user=talent_user,
                participants__room__is_active=True
            ).exists()

            if room_exists:
                continue

            room_name = f"Chat: {campaign.campaign_name}"
            room = Room.objects.create(name=room_name, campaign=campaign)

            RoomParticipant.objects.create(room=room, user=request.user, role='brand')
            RoomParticipant.objects.create(room=room, user=talent_user, role='talent')

            created_rooms.append(room)
            valid_campaign_ids.append(campaign_id)

        if not created_rooms:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='No new rooms were created (maybe all already exist)',
                data={}
            )

        proposal_send = request.query_params.get("proposal_send", "false").lower() == "true"
        if proposal_send:
            create_proposal_versions_and_applications(
                campaign_ids=valid_campaign_ids,
                sent_by=request.user,
                sent_to=talent_user
            )

        serializer = RoomSerializer(created_rooms, many=True)
        return Responses.success_response(
            status=status.HTTP_201_CREATED,
            message='Rooms and proposal versions created successfully',
            data={
                "rooms": serializer.data,
            }
        )



    def delete(self, request, room_id):
        """Deactivate a room"""
        try:
            room = Room.objects.get(id=room_id)
            
            # Check if user is a participant
            if not room.participants.filter(user=request.user, is_active=True).exists():
                return Responses.error_response(
                    status=status.HTTP_403_FORBIDDEN,
                    message='You are not a participant in this room',
                    data={}
                )

            # Soft delete by setting is_active to False
            room.is_active = False
            room.save()

            # Also deactivate all participants
            room.participants.all().update(is_active=False)

            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Room deactivated successfully',
                data={}
            )
        except Room.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Room not found',
                data={}
            )

class GreetingMessageView(APIView):
    """View for handling greeting messages"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get greeting messages for a talent"""
        # Check if user is a brand
        if request.user.user_type != 'B':
            return Responses.error_response(
                status=status.HTTP_403_FORBIDDEN,
                message='Only brands can access greeting messages',
                data={}
            )

        serializer = GreetingMessageSerializer(data=request.data)
        if not serializer.is_valid():
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Invalid data',
                data=serializer.errors
            )

        try:
            talent = User.objects.get(id=serializer.validated_data['talent_id'])
            talent_name = talent.talent_profile.name if hasattr(talent, 'talent_profile') else talent.email

            greeting_messages = serializer.get_greeting_messages(talent_name)

            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Greeting messages retrieved successfully',
                data={
                    'talent_name': talent_name,
                    'messages': greeting_messages
                }
            )
        except User.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Talent not found',
                data={}
            )


class ActiveCampaignProposalsAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, talent_id):
        # Statuses to exclude
        excluded_statuses = [
            ApplicationStatusChoices.ACTIVE,
            ApplicationStatusChoices.ACCEPTED,
            ApplicationStatusChoices.REJECTED,
        ]

        # Get campaigns this talent has interacted with
        excluded_campaign_ids = TalentCampaignApplication.objects.filter(
            talent_id=talent_id,
            status__in=excluded_statuses
        ).values_list('campaign_id', flat=True)

        # Get proposals for ACTIVE campaigns not already interacted with
        proposals = Proposal.objects.filter(
            campaign__status=CampaignStatusChoices.ACTIVE
        ).exclude(
            campaign__id__in=excluded_campaign_ids
        ).select_related('campaign').prefetch_related('categories')

        serializer = ActiveCampaignProposalSerializer(proposals, many=True)
        return Response(serializer.data)


class ProductDeliveryViewSet(ModelViewSet):
    """
    ProductDelivery ViewSet - handles all product delivery operations
    """
    serializer_class = ProductDeliverySerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        user = self.request.user
        if hasattr(user, 'talent_profile'):
            return ProductDelivery.objects.filter(talent=user)
        elif hasattr(user, 'brand_profile'):
            return ProductDelivery.objects.filter(brand=user)
        return ProductDelivery.objects.none()

    def create(self, request, *args, **kwargs):
        """Create product delivery with address entry"""
        user = request.user
        proposal_version_id = request.data.get('proposal_version')
        
        try:
            proposal_version = ProposalVersion.objects.get(id=proposal_version_id)
        except ProposalVersion.DoesNotExist:
            return Response({'error': 'Proposal version not found'}, status=404)
        
        # Only talents can create delivery entries
        if not hasattr(user, 'talent_profile'):
            return Response({'error': 'Only talents can enter delivery address'}, status=403)
        
        # Check if product delivery is enabled
        if not proposal_version.barter_products.exists():
            return Response({'error': 'No product delivery enabled for this proposal'}, status=400)
        
        # Check if delivery already exists
        if ProductDelivery.objects.filter(proposal_version=proposal_version, talent=user).exists():
            return Response({'error': 'Product delivery already exists'}, status=400)
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        delivery = serializer.save(talent=user, brand=proposal_version.sent_to)
        return Response(serializer.data, status=201)

    def update(self, request, *args, **kwargs):
        """Update product delivery"""
        instance = self.get_object()
        user = request.user
        
        # Validate user permissions
        if hasattr(user, 'talent_profile') and instance.talent != user:
            return Response({'error': 'Access denied'}, status=403)
        elif hasattr(user, 'brand_profile') and instance.brand != user:
            return Response({'error': 'Access denied'}, status=403)
        
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        # Handle receipt confirmation by talent
        if hasattr(user, 'talent_profile') and 'receipt_status' in request.data:
            receipt_status = request.data['receipt_status']
            if receipt_status not in ['received', 'not_received']:
                return Response({'error': 'Invalid receipt status'}, status=400)
            
            if not instance.can_confirm_receipt:
                return Response({'error': 'Cannot confirm receipt at this time'}, status=400)
            
            instance.confirm_receipt(receipt_status)
            return Response(serializer.data)
        
        # Handle other updates
        serializer.save()
        return Response(serializer.data)

class ChecklistItemViewSet(ModelViewSet):
    """
    ModelViewSet for ChecklistItem model
    Brands can create checklists, both brands and talents can view and update
    Extra checklist items are handled within the same view
    """
    serializer_class = ChecklistItemSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        user = self.request.user
        
        # Brands can see checklists for their campaigns
        if hasattr(user, 'brand_profile'):
            return ChecklistItem.objects.filter(
                deliverable__proposal_version__proposal__campaign__brand=user
            ).select_related(
                'deliverable__proposal_version__proposal__campaign__brand'
            ).prefetch_related('extra_checklist')
        
        # Talents can see checklists for deliverables they have access to
        elif hasattr(user, 'talent_profile'):
            # This logic can be enhanced based on your assignment system
            # For now, allowing talents to see all checklists
            return ChecklistItem.objects.all().select_related(
                'deliverable__proposal_version__proposal__campaign__brand'
            ).prefetch_related('extra_checklist')
        
        return ChecklistItem.objects.none()

    def list(self, request, *args, **kwargs):
        """List all checklists for the user"""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset, many=True)
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Checklists retrieved successfully',
                data=serializer.data
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to retrieve checklists',
                data={'error': str(e)}
            )

    def create(self, request, *args, **kwargs):
        """Create a new checklist with extra items"""
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                self.perform_create(serializer)
                return Responses.success_response(
                    status=status.HTTP_201_CREATED,
                    message='Checklist created successfully',
                    data=serializer.data
                )
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Validation error',
                data=serializer.errors
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to create checklist',
                data={'error': str(e)}
            )

    def retrieve(self, request, *args, **kwargs):
        """Get a specific checklist"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Checklist retrieved successfully',
                data=serializer.data
            )
        except ChecklistItem.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Checklist not found',
                data={}
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to retrieve checklist',
                data={'error': str(e)}
            )

    def update(self, request, *args, **kwargs):
        """Update a checklist"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            if serializer.is_valid():
                self.perform_update(serializer)
                return Responses.success_response(
                    status=status.HTTP_200_OK,
                    message='Checklist updated successfully',
                    data=serializer.data
                )
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Validation error',
                data=serializer.errors
            )
        except ChecklistItem.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Checklist not found',
                data={}
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to update checklist',
                data={'error': str(e)}
            )

    def destroy(self, request, *args, **kwargs):
        """Delete a checklist"""
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Checklist deleted successfully',
                data={}
            )
        except ChecklistItem.DoesNotExist:
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Checklist not found',
                data={}
            )
        except Exception as e:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message='Failed to delete checklist',
                data={'error': str(e)}
            )

    def perform_create(self, serializer):
        # Only brands can create checklists
        user = self.request.user
        if not hasattr(user, 'brand_profile'):
            raise ValidationError("Only brands can create checklists")
        
        # Validate that the brand owns the campaign
        deliverable = serializer.validated_data['deliverable']
        campaign = deliverable.proposal_version.proposal.campaign
        
        if campaign.brand != user:
            raise ValidationError("You can only create checklists for your own campaigns")
        
        serializer.save()

    def perform_update(self, serializer):
        # Both brands and talents can update checklists
        user = self.request.user
        instance = serializer.instance
        
        if hasattr(user, 'brand_profile'):
            # Brand can update if they own the campaign
            campaign = instance.deliverable.proposal_version.proposal.campaign
            if campaign.brand != user:
                raise ValidationError("You can only update checklists for your own campaigns")
        elif hasattr(user, 'talent_profile'):
            # Talent can update (you might want to add more specific logic here)
            pass
        else:
            raise ValidationError("You don't have permission to update this checklist")
        
        serializer.save()
    

class ScriptAPIView(ModelViewSet):
    """View for handling scripts"""
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'
    serializer_class = ScriptSerializer

    def get_queryset(self):
        queryset = Script.objects.select_related(
            "proposal_version",
            "proposal_version__proposal",
            "proposal_version__proposal__campaign",
            "sent_by",
            "sent_to"
        )

        proposal_version_id = self.request.query_params.get("proposal_version")
        if proposal_version_id:
            queryset = queryset.filter(proposal_version_id=proposal_version_id)

        return queryset
    
    def list(self, request, *args, **kwargs):
        """Return a list of scripts (no pagination)"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Scripts fetched successfully.",
            data=serializer.data
        )

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        new_status = request.data.get("status")

        try:
            new_status = int(new_status) if new_status is not None else None
        except (ValueError, TypeError):
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Status must be a valid integer.",
                data={"status": "Status must be a valid integer."}
            )

        valid_statuses = [ScriptStatus.FINALISED, ScriptStatus.TERMINATE, ScriptStatus.REJECTED]

        if new_status not in valid_statuses:
            return Responses.error_response(
                status=status.HTTP_400_BAD_REQUEST,
                message="Status must be Finalised, Terminated, or Rejected.",
                data={"status": "Invalid status."}
            )

        instance.status = new_status
        if new_status == ScriptStatus.FINALISED:
            instance.accepted_by = request.user
        elif new_status == ScriptStatus.TERMINATE:
            instance.terminated_by = request.user
        elif new_status == ScriptStatus.REJECTED:
            instance.rejected_by = request.user

        instance.save(update_fields=[
            "status", 
            "accepted_by", 
            "terminated_by", 
            "rejected_by", 
            "updated_at"
        ])

        serializer = self.get_serializer(instance)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message="Script status updated successfully.",
            data=serializer.data
        )

class ContentUploadViewSet(ModelViewSet):
    """
    ViewSet for managing content uploads
    Handles the workflow: Talent uploads → Brand reviews → Approval/Rejection
    """
    serializer_class = ContentUploadSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        user = self.request.user
        
        # Brands can see content sent to them
        if hasattr(user, 'brand_profile'):
            return ContentUpload.objects.filter(sent_to=user)
        
        # Talents can see content they sent
        elif hasattr(user, 'talent_profile'):
            return ContentUpload.objects.filter(sent_by=user)
        
        return ContentUpload.objects.none()
    
    def list(self, request, *args, **kwargs):
        """List content uploads with filtering options"""
        queryset = self.get_queryset()
        
        # Filter by status
        status_filter = request.query_params.get('status')
        if status_filter:
            try:
                status_int = int(status_filter)
                queryset = queryset.filter(status=status_int)
            except ValueError:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Invalid status value',
                    data={}
                )
        
        # Filter by script
        script_id = request.query_params.get('script_id')
        if script_id:
            try:
                script_int = int(script_id)
                queryset = queryset.filter(script_id=script_int)
            except ValueError:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Invalid script ID',
                    data={}
                )
        
        # Filter by deliverable
        deliverable_id = request.query_params.get('deliverable_id')
        if deliverable_id:
            try:
                deliverable_int = int(deliverable_id)
                queryset = queryset.filter(deliverable_id=deliverable_int)
            except ValueError:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Invalid deliverable ID',
                    data={}
                )
        
        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Content uploads retrieved successfully',
            data=serializer.data
        )
    
    def create(self, request, *args, **kwargs):
        """Create a new content upload (Talent only)"""
        # Only talents can create content uploads
        if not hasattr(request.user, 'talent_profile'):
            return Responses.error_response(
                status=status.HTTP_403_FORBIDDEN,
                message='Only talents can upload content',
                data={}
            )
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            content_upload = serializer.save()
            return Responses.success_response(
                status=status.HTTP_201_CREATED,
                message='Content uploaded successfully',
                data=serializer.data
            )
        return Responses.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message='Invalid data provided',
            data=serializer.errors
        )
    
    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific content upload"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Content upload retrieved successfully',
            data=serializer.data
        )

    def partial_update(self, request, *args, **kwargs):
        """Update a content upload (partial update) - Both talent and brand can update"""
        instance = self.get_object()
        user = request.user
        
        # Check permissions based on user type
        if hasattr(user, 'talent_profile'):
            # Talent can update their own content
            if instance.sent_by != user:
                return Responses.error_response(
                    status=status.HTTP_403_FORBIDDEN,
                    message='You can only update your own content uploads',
                    data={}
                )
            
            # Talent can update content and feedback for SENT and REJECTED status
            if instance.status not in [ContentUploadStatus.SENT, ContentUploadStatus.REJECTED]:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='Cannot update content that has been finalized',
                    data={}
                )
                
        elif hasattr(user, 'brand_profile'):
            # Brand can update status and feedback for content sent to them
            if instance.sent_to != user:
                return Responses.error_response(
                    status=status.HTTP_403_FORBIDDEN,
                    message='You can only update content sent to you',
                    data={}
                )
            
            # Brand can only update status and feedback, not content
            allowed_fields = ['status', 'feedback']
            for field in request.data.keys():
                if field not in allowed_fields:
                    return Responses.error_response(
                        status=status.HTTP_400_BAD_REQUEST,
                        message=f'Brands can only update: {", ".join(allowed_fields)}',
                        data={}
                    )
        else:
            return Responses.error_response(
                status=status.HTTP_403_FORBIDDEN,
                message='Access denied',
                data={}
            )
        
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            
            # Generate appropriate message based on what was updated
            if hasattr(user, 'brand_profile') and 'status' in request.data:
                status = request.data['status']
                if status == ContentUploadStatus.FINALISED:
                    message = 'Content approved successfully'
                elif status == ContentUploadStatus.REJECTED:
                    message = 'Content rejected successfully'
                else:
                    message = 'Content status updated successfully'
            else:
                message = 'Content upload updated successfully'
                
            return Responses.success_response(
                status=status.HTTP_200_OK,
                message=message,
                data=serializer.data
            )
        return Responses.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message='Invalid data provided',
            data=serializer.errors
        )





class ContentUploadHistoryView(APIView):
    """
    View for getting content upload history for a specific deliverable and script
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, script_id, deliverable_id):
        """Get content upload history"""
        try:
            script = Script.objects.get(id=script_id)
            deliverable = ProposalVersionDeliverableItem.objects.get(id=deliverable_id)
        except (Script.DoesNotExist, ProposalVersionDeliverableItem.DoesNotExist):
            return Responses.error_response(
                status=status.HTTP_404_NOT_FOUND,
                message='Script or deliverable not found',
                data={}
            )
        
        # Check permissions
        user = request.user
        if hasattr(user, 'talent_profile'):
            # Talent can see their own content
            if script.sent_by != user and script.sent_to != user:
                return Responses.error_response(
                    status=status.HTTP_403_FORBIDDEN,
                    message='Access denied',
                    data={}
                )
        elif hasattr(user, 'brand_profile'):
            # Brand can see content related to their campaigns
            if script.proposal_version.proposal.campaign.brand != user:
                return Responses.error_response(
                    status=status.HTTP_403_FORBIDDEN,
                    message='Access denied',
                    data={}
                )
        else:
            return Responses.error_response(
                status=status.HTTP_403_FORBIDDEN,
                message='Access denied',
                data={}
            )
        
        # Get content history
        from .services import ContentUploadService
        content_history = ContentUploadService.get_content_history(deliverable, script)
        
        serializer = ContentUploadHistorySerializer(content_history, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Content history retrieved successfully',
            data=serializer.data
        )

class CampaignReviewViewSet(ModelViewSet):
    queryset = CampaignReview.objects.all()
    serializer_class = CampaignReviewSerializer
    permission_classes = [IsAuthenticated]
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()
        campaign_id = self.request.query_params.get('campaign')
        reviewee_id = self.request.query_params.get('reviewee')
        if campaign_id:
            queryset = queryset.filter(campaign_id=campaign_id)
        if reviewee_id:
            queryset = queryset.filter(reviewee_id=reviewee_id)
        return queryset

    def create(self, request, *args, **kwargs):
        """Create a new campaign review"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Check if review already exists
            existing_review = CampaignReview.objects.filter(
                campaign=serializer.validated_data['campaign'],
                reviewer=request.user,
                reviewee=serializer.validated_data['reviewee']
            ).first()
            
            if existing_review:
                return Responses.error_response(
                    status=status.HTTP_400_BAD_REQUEST,
                    message='You have already reviewed this user for this campaign',
                    data={}
                )
            
            review = serializer.save(reviewer=request.user)
            return Responses.success_response(
                status=status.HTTP_201_CREATED,
                message='Review submitted successfully',
                data=serializer.data
            )
        return Responses.error_response(
            status=status.HTTP_400_BAD_REQUEST,
            message='Invalid data provided',
            data=serializer.errors
        )

    def list(self, request, *args, **kwargs):
        """List campaign reviews"""
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Reviews retrieved successfully',
            data=serializer.data
        )

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific campaign review"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Responses.success_response(
            status=status.HTTP_200_OK,
            message='Review retrieved successfully',
            data=serializer.data
        )


class CampaignTypeAPIView(APIView):
    """
    API view to get available campaign types
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get all available campaign types
        """
        try:
            # Get campaign type choices
            campaign_types = [
                {
                    'value': choice[0],
                    'label': choice[1]
                }
                for choice in CampaignTypeChoices.choices
            ]

            return Responses.success_response(
                status=status.HTTP_200_OK,
                message='Campaign types retrieved successfully',
                data={
                    'campaign_types': campaign_types
                }
            )

        except Exception as e:
            logger.error(f"Error retrieving campaign types: {str(e)}")
            return Responses.error_response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message='Failed to retrieve campaign types',
                data={'error': str(e)}
            )

