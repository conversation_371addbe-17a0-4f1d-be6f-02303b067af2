from django.db import transaction
from decimal import Decimal

from accounts.models import TalentProfile
from campaigns.models import CampaignProposalVersionProduct, Proposal, ProposalNegotiationStatus, ProposalVersion, ProposalVersionDeliverable, ProposalVersionDeliverableItem, ProposalVersionPlatform, TalentCampaignApplication
from talent_trade.utils.choices import ApplicationStatusChoices

@transaction.atomic
def create_proposal_versions_and_applications(campaign_ids, sent_by, sent_to):
    talent_profile = TalentProfile.objects.get(user=sent_to)
    created_versions = []

    for campaign_id in campaign_ids:
        try:
            proposal = Proposal.objects.prefetch_related(
                'platforms',
                'deliverables__items',
                'proposal_products' 
            ).select_related('campaign').get(campaign_id=campaign_id)
        except Proposal.DoesNotExist:
            continue

        # Create ProposalVersion (triggers signal)
        version = ProposalVersion.objects.create(
            proposal=proposal,
            sent_by=sent_by,
            sent_to=sent_to,
            duration=proposal.duration,
            duration_type=proposal.duration_type,
            offer_amount_min=proposal.initial_offer_min,
            offer_amount_max=proposal.initial_offer_max,
            status=ProposalNegotiationStatus.SENT
        )
        created_versions.append(version)

        # Bulk insert platforms
        platforms = [
            ProposalVersionPlatform(
                proposal_version=version,
                platform=p.platform,
            )
            for p in proposal.platforms.all()
        ]
        ProposalVersionPlatform.objects.bulk_create(platforms)

        # Insert deliverables and their items
        for deliverable in proposal.deliverables.all():
            version_deliverable = ProposalVersionDeliverable.objects.create(
                proposal_version=version,
                name=deliverable.name,
                is_negotiable=False
            )
            items = [
                ProposalVersionDeliverableItem(
                    deliverable=version_deliverable,
                    platform=item.platform,
                    category=item.category,
                    quantity=item.quantity,
                    is_negotiable=False
                )
                for item in deliverable.items.all()
            ]
            ProposalVersionDeliverableItem.objects.bulk_create(items)
        
        # Create barter products from base proposal
        barter_products = [
            CampaignProposalVersionProduct(
                proposal_version=version,
                name=product.name,
                description=product.description,
                image=product.image,
                quantity_available=product.quantity_available,
                is_non_negotiable=product.is_non_negotiable
            )
            for product in proposal.proposal_products.all()
        ]
        CampaignProposalVersionProduct.objects.bulk_create(barter_products)


        # Application with ACTIVE status
        TalentCampaignApplication.objects.update_or_create(
            talent=talent_profile,
            campaign=proposal.campaign,
            defaults={'status': ApplicationStatusChoices.ACTIVE}
        )

    return created_versions
