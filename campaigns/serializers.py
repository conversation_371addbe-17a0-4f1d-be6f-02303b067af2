from accounts.choices import UserTypeChoices
from accounts.models import BrandProfile, Language, PlatformCategory, SocialMediaPlatform,TalentProfile
from accounts.serializers import LanguageSerializer
from campaigns.models import *
from rest_framework import serializers
from django.utils import timezone
from django.db import transaction
from campaigns.models import CampaignBookmark  
from talent_trade.utils.choices import CampaignStatusChoices, KYCStatusChoices, ContentUploadStatus
from talent_trade.utils.custom_serilaizer import Option<PERSON><PERSON>iceField
from .models import CampaignReview


    
class CampaignBasicDetailsSerializer(serializers.ModelSerializer):
    """Serializer for basic campaign details (Step 1)"""

    category = serializers.PrimaryKeyRelatedField(many=True, queryset=ExpertiseArea.objects.filter(is_active=True))
    campaign_type = OptionChoiceField(choices=CampaignTypeChoices.choices,default=CampaignTypeChoices.PAID)
    campaign_banner = serializers.CharField(required=False)
    class Meta:
        model = Campaign
        fields = ['id','campaign_name', 'brief', 'hashtags', 'final_submission_date', 'preferred_state', 'preferred_city','category','campaign_type','campaign_banner']

    def validate_campaign_name(self, value):
        if not value or len(value.strip()) < 3:
            raise serializers.ValidationError("Campaign name must be at least 3 characters long")
        return value.strip()

    def validate_brief(self, value):
        if value and len(value.strip()) > 3000:
            raise serializers.ValidationError("Brief cannot exceed 3000 characters")
        return value.strip() if value else value

    # def validate_hashtags(self, value):
    #     if value:
    #         # Remove spaces after commas and trim
    #         cleaned_hashtags = ','.join(tag.strip() for tag in value.split(','))
    #         if len(cleaned_hashtags) > 512:
    #             raise serializers.ValidationError("Total hashtags length cannot exceed 512 characters")
    #         return cleaned_hashtags
    #     return value

    def validate_final_submission_date(self, value):
        if not value:
            raise serializers.ValidationError("Final submission date is required")
        if value <= timezone.now().date():
            raise serializers.ValidationError("Final submission date must be in the future")
        return value

    def validate(self, data):
        if self.partial and 'final_submission_date' not in data:
            raise serializers.ValidationError({"final_submission_date": "This field is required"})
        return data
    
class CampaignLanguagesSerializer(serializers.ModelSerializer):
    """Serializer for campaign languages (Step 2)"""
    languages = serializers.PrimaryKeyRelatedField(many=True, queryset=Language.objects.filter(is_active=True))

    class Meta:
        model = Campaign
        fields = ['languages']

    def validate_languages(self, value):
        if not value:
            raise serializers.ValidationError("At least one language must be selected")
        return value

class CampaignBudgetSerializer(serializers.ModelSerializer):
    """Serializer for campaign budget (Step 3)"""
    class Meta:
        model = Campaign
        fields = ['min_budget', 'max_budget']

    def validate(self, data):
        min_budget = data.get('min_budget')
        max_budget = data.get('max_budget')

        if min_budget is not None and max_budget is not None:
            if min_budget < 0:
                raise serializers.ValidationError({"min_budget": "Minimum budget cannot be negative"})
            if max_budget < 0:
                raise serializers.ValidationError({"max_budget": "Maximum budget cannot be negative"})
            if min_budget > max_budget:
                raise serializers.ValidationError({"max_budget": "Maximum budget must be greater than minimum budget"})
            
            if (max_budget - min_budget) < 1000000:
                raise serializers.ValidationError({
                    "max_budget": "There must be at least ₹10,00,000 difference between min and max budget."
                })

        return data

class CampaignDeliverableSerializer(serializers.ModelSerializer):
    """Serializer for campaign deliverables (Step 4)"""
    platform_id = serializers.PrimaryKeyRelatedField(source='platform', queryset=SocialMediaPlatform.objects.all())
    category_id = serializers.PrimaryKeyRelatedField(source='category_type', queryset=PlatformCategory.objects.all())

    class Meta:
        model = CampaignDeliverable
        fields = ['platform_id', 'category_id', 'count']

    def validate_count(self, value):
        if value < 1:
            raise serializers.ValidationError("Count must be at least 1")
        return value

    def validate(self, data):
        # Ensure the category belongs to the selected platform
        platform = data.get('platform')
        category = data.get('category_type')
        
        if platform and category and not category.platform == platform:
            raise serializers.ValidationError(
                {"category_id": f"This category does not belong to {platform.name}"}
            )
        
        return data

class GoLiveDateSerializer(serializers.ModelSerializer):
    """Serializer for campaign go live dates (Step 5)"""
    deliverable_id = serializers.PrimaryKeyRelatedField(source='deliverable', queryset=CampaignDeliverable.objects.all())

    class Meta:
        model = CampaignGoLiveDate
        fields = ['deliverable_id', 'go_live_date', 'is_active']

    def validate_go_live_date(self, value):
        if value <= timezone.now().date():
            raise serializers.ValidationError("Go live date must be in the future")
        return value

    def validate(self, data):
        campaign = self.context.get('campaign')
        go_live_date = data.get('go_live_date')
        
        if campaign and go_live_date:
            if go_live_date > campaign.final_submission_date:
                raise serializers.ValidationError(
                    {"go_live_date": "Go live date cannot be after campaign final submission date"}
                )
        
        return data

class CampaignRetrieveSerializer(serializers.ModelSerializer):
    """Serializer for retrieving complete campaign with all steps"""
    step_1 = serializers.SerializerMethodField()
    step_2 = serializers.SerializerMethodField()
    step_3 = serializers.SerializerMethodField()
    step_4 = serializers.SerializerMethodField()
    step_6 = serializers.SerializerMethodField()
    # step_5 = serializers.SerializerMethodField()
    # profile_completion = serializers.SerializerMethodField()
    is_campaign_bookmarked = serializers.SerializerMethodField()
    is_campaign_applied = serializers.SerializerMethodField()
    
    class Meta:
        model = Campaign
        fields = ['id', 'step_1', 'step_2', 'step_3', 'step_4', 'step_6', 'status', 'is_campaign_bookmarked','is_campaign_applied']

    def get_is_campaign_applied(self, obj):
        request = self.context.get('request')
        if not request:
            return False

        try:
            talent_profile = request.user.talent_profile
        except TalentProfile.DoesNotExist:
            return False

        # Check if application exists for this campaign
        return TalentCampaignApplication.objects.filter(
            campaign=obj,
            talent=talent_profile
        ).exclude(status=ApplicationStatusChoices.UNAPPLIED).exists()

    def calculate_match_percentage(self, campaign, user):
        profile = getattr(user, 'talent_profile', None)
        if not profile:
            return 0, []

        score = 0
        total_criteria = 2  # updated since tags are commented out
        matched_criteria = []

        # Tags / Interest Areas match — temporarily commented out
        # campaign_tags = set(campaign.tags.values_list('id', flat=True))
        # user_tags = set(profile.expertise_areas.values_list('id', flat=True))
        # if campaign_tags and user_tags:
        #     if campaign_tags.intersection(user_tags):
        #         score += 1
        #         matched_criteria.append('Tags/Interest Areas')

        # Platform match
        campaign_platforms = set(campaign.platform_selections.values_list('platform__id', flat=True))
        user_platforms = set(user.social_connects.filter(connected=True).values_list('platform__id', flat=True))
        if campaign_platforms and user_platforms:
            if campaign_platforms.intersection(user_platforms):
                score += 1
                matched_criteria.append('Selected Platforms')

        # Language match
        campaign_languages = set(campaign.languages.values_list('id', flat=True))
        user_languages = set(profile.languages.values_list('id', flat=True))
        if campaign_languages and user_languages:
            if campaign_languages.intersection(user_languages):
                score += 1
                matched_criteria.append('Languages Known')

        percentage = int((score / total_criteria) * 100)
        return percentage, matched_criteria



    def get_step_1(self, obj):
        """Basic campaign details"""

        brand_logo_name = None
        brand_name = None
        brand_profile = getattr(obj.brand, 'brand_profile', None)
        if brand_profile and brand_profile.brand_logo:
            brand_logo_name = brand_profile.brand_logo.name
            brand_name = brand_profile.brand_name

        gender_label = GenderChoices.get_full_value_from_short(obj.preferred_gender)
        campaign_banner_url = obj.campaign_banner.name if obj.campaign_banner else None
        # category_name = obj.category.name if obj.category else None
        category_list = list(obj.category.values('id', 'name'))
        data = {
            'id': obj.id,
            'campaign_name': obj.campaign_name,
            'brief': obj.brief,
            'hashtags': obj.hashtags,
            'final_submission_date': obj.final_submission_date,
            'preferred_state': obj.preferred_state,
            'preferred_city': obj.preferred_city,
            'brand_logo': brand_logo_name,
            'brand_name': brand_name,
            'preferred_gender' : gender_label,
            'campaign_banner': campaign_banner_url,
            'category': category_list,
            'campaign_type' : obj.campaign_type
        }

        user = self.context['request'].user
        if hasattr(user, 'talent_profile'):
            match_percentage, match_factors = self.calculate_match_percentage(obj, user)
            data['match_percentage'] = match_percentage

        return data

    def get_step_2(self, obj):
        """Languages"""
        return {
            'languages': LanguageSerializer(obj.languages.all(), many=True).data
        }

    def get_step_3(self, obj):
        """Budget"""
        return {
            'min_budget': obj.min_budget,
            'max_budget': obj.max_budget
        }

    def get_step_4(self, obj):
        """Deliverables grouped by platform with platform icon"""
        request = self.context.get('request')  
        platforms_data = []

        platform_selections = obj.platform_selections.all()

        for platform_selection in platform_selections:
            platform = platform_selection.platform
            platform_name = platform.name

            # Handle full URL for icon if available
            icon_url = platform.icon.name if platform.icon else None
            if icon_url and request is not None:
                icon_url = icon_url

            deliverables_data = []

            for deliverable in platform_selection.deliverables.all():
                go_live_dates_data = [
                    {
                        "go_live_date": go_live_date.go_live_date,
                        "is_active": go_live_date.is_active
                    }
                    for go_live_date in deliverable.go_live_dates.all()
                ]

                deliverables_data.append({
                    "deliverable_id": deliverable.id,
                    "category": deliverable.category_type.name,
                    "count": deliverable.count,
                    "go_live_dates": go_live_dates_data
                })

            platforms_data.append({
                "platform": platform_name,
                "platform_icon": icon_url,
                "deliverables": deliverables_data
            })

        return {"platforms": platforms_data}

    def get_step_6(self, obj):
        """Campaign products for delivery"""
        products = obj.campaign_products.all()
        return {
            'products': CampaignProductSerializer(products, many=True, context=self.context).data
        }

    # def get_step_5(self, obj):
    #     """Go live dates"""
    #     go_live_dates = []
    #     for platform_selection in obj.platform_selections.all():
    #         for deliverable in platform_selection.deliverables.all():
    #             for date in deliverable.go_live_dates.all():
    #                 go_live_dates.append({
    #                     'deliverable_id': deliverable.id,
    #                     'go_live_date': date.go_live_date,
    #                     'is_active': date.is_active
    #                 })
    #     return {'go_live_dates': go_live_dates}
    
    def get_is_campaign_bookmarked(self, obj):
        user = self.context['request'].user
        if hasattr(user, 'talent_profile'):
            return CampaignBookmark.objects.filter(talent=user, campaign=obj).exists()
        return False
    

    
    # def get_profile_completion(self, obj):
    #     """Calculate campaign completion percentage"""
    #     total_steps = 5
    #     completed_steps = 0
        
    #     # Check step 1 (basic details)
    #     if obj.campaign_name and obj.brief and obj.final_submission_date:
    #         completed_steps += 1
            
    #     # Check step 2 (languages)
    #     if obj.languages.exists():
    #         completed_steps += 1
            
    #     # Check step 3 (budget)
    #     if obj.min_budget is not None and obj.max_budget is not None:
    #         completed_steps += 1
            
    #     # Check step 4 (deliverables)
    #     if obj.platform_selections.exists() and any(ps.deliverables.exists() for ps in obj.platform_selections.all()):
    #         completed_steps += 1
            
    #     # Check step 5 (go live dates)
    #     has_active_dates = False
    #     for ps in obj.platform_selections.all():
    #         for deliverable in ps.deliverables.all():
    #             if deliverable.go_live_dates.filter(is_active=True).exists():
    #                 has_active_dates = True
    #                 break
    #         if has_active_dates:
    #             break
    #     if has_active_dates:
    #         completed_steps += 1
            
    #     return int((completed_steps / total_steps) * 100)

class HelperCampaignSerializer(serializers.ModelSerializer):
    platforms = serializers.SerializerMethodField()
    brand_name = serializers.SerializerMethodField()
    brand_logo = serializers.SerializerMethodField()
    campaign_banner = serializers.SerializerMethodField()
    category = serializers.SerializerMethodField()
    preferred_gender = serializers.SerializerMethodField()
    campaign_type = OptionChoiceField(choices=CampaignTypeChoices.choices,default=CampaignTypeChoices.PAID)


    class Meta:
        model = Campaign
        fields = [
            'id', 'campaign_name', 'brief', 'preferred_state', 'preferred_city',
            'min_budget', 'max_budget', 'platforms', 'brand_name', 'brand_logo',
            'campaign_banner', 'category', 'preferred_gender','campaign_type'
        ]

    def get_platforms(self, obj):
        platform_qs = SocialMediaPlatform.objects.filter(
            campaign_platforms__campaign=obj
        ).distinct()
        return [
            {
                'name': platform.name,
                'icon': platform.icon.name if platform.icon else None
            }
            for platform in platform_qs
        ]

    def get_brand_name(self, obj):
        brand_profile = getattr(obj.brand, 'brand_profile', None)
        return brand_profile.brand_name if brand_profile else ''

    def get_brand_logo(self, obj):
        brand_profile = getattr(obj.brand, 'brand_profile', None)
        if brand_profile and brand_profile.brand_logo:
            return brand_profile.brand_logo.name
        return None

    def get_campaign_banner(self, obj):
        return obj.campaign_banner.name if obj.campaign_banner else None

    def get_category(self, obj):
        return obj.category.name if obj.category else None

    def get_preferred_gender(self, obj):
        return GenderChoices.get_full_value_from_short(obj.preferred_gender)

        
class TalentCampaignApplicationSerializer(serializers.ModelSerializer):
    campaign = serializers.PrimaryKeyRelatedField(queryset=Campaign.objects.all(), write_only=True)
    
    campaign_details = HelperCampaignSerializer(source='campaign', read_only=True)
    
    status = OptionChoiceField(choices=ApplicationStatusChoices.choices, default=ApplicationStatusChoices.APPLIED)

    class Meta:
        model = TalentCampaignApplication
        fields = ['id', 'talent', 'campaign', 'campaign_details', 'status']
        read_only_fields = ['talent', 'status'] 

    def validate(self, data):
        user = self.context['request'].user

        if not hasattr(user, 'talent_profile'):
            raise serializers.ValidationError("Only talent users can apply to campaigns.")

        talent_profile = user.talent_profile
        campaign = data['campaign']

        if TalentCampaignApplication.objects.filter(talent=talent_profile, campaign=campaign).exists():
            raise serializers.ValidationError("You have already applied to this campaign.")

        if campaign.status != CampaignStatusChoices.ACTIVE:
            raise serializers.ValidationError("You can only apply to active campaigns.")
        
        if not hasattr(user, 'bank_account_details'):
            raise serializers.ValidationError("Please complete your bank account details before applying.")
        
        if user.kyc_status != KYCStatusChoices.VERIFIED:
            raise serializers.ValidationError("Please complete your KYC before applying to a campaign.")

        basic_fields = ['name',]
        missing = [field for field in basic_fields if not getattr(talent_profile, field)]
        if missing:
            raise serializers.ValidationError(
                f"Please complete your profile details: {', '.join(missing)}."
            )

        return data

    def create(self, validated_data):
        validated_data['talent'] = self.context['request'].user.talent_profile
        return super().create(validated_data)



class CampaignApplicantTalentSerializer(serializers.ModelSerializer):
    profile_pic = serializers.SerializerMethodField()
    application_status = serializers.IntegerField(read_only=True)
    application_status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = TalentProfile
        fields = ['id', 'name', 'profile_pic', 'bio', 'state', 'city', 'application_status', 'application_status_display']
        
    def get_profile_pic(self, obj):
        return obj.profile_pic.name if obj.profile_pic else None
        
    def get_application_status_display(self, obj):
        from talent_trade.utils.choices import ApplicationStatusChoices
        status_dict = dict(ApplicationStatusChoices.choices)
        return status_dict.get(obj.application_status, "Unknown")
    

class DeliverableItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProposalDeliverableItem
        fields = ['platform', 'category', 'quantity']


class DeliverableSerializer(serializers.ModelSerializer):
    items = DeliverableItemSerializer(many=True)

    class Meta:
        model = ProposalDeliverable
        fields = ['name', 'items']


class ProposalPlatformSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProposalPlatform
        fields = ['platform']

class BarterProductSerializer(serializers.ModelSerializer):
    image = serializers.CharField(required=False)
    class Meta:
        model = CampaignProposalProduct
        fields = ['name', 'description', 'quantity_available', 'image', 'is_non_negotiable']

class ProposalSerializer(serializers.ModelSerializer):
    platforms = ProposalPlatformSerializer(many=True)
    platform_is_negotiable = serializers.BooleanField()
    deliverable_is_negotiable = serializers.BooleanField()
    deliverables = DeliverableSerializer(many=True)
    proposal_products = BarterProductSerializer(many=True,required=False)

    class Meta:
        model = Proposal
        fields = [
            'id',
            'campaign',
            'duration',
            'duration_type',
            'duration_is_negotiable',
            'categories',
            'deliverable_is_negotiable',
            'initial_offer_min',
            'initial_offer_max',
            'offer_is_negotiable',
            'platform_is_negotiable',
            'platforms',
            'deliverables',
            'proposal_products'
        ]


    def create(self, validated_data):
        platforms_data = validated_data.pop('platforms')
        deliverables_data = validated_data.pop('deliverables')
        categories_data = validated_data.pop('categories')
        barter_products_data = validated_data.pop('proposal_products', [])

        if Proposal.objects.filter(campaign=validated_data['campaign']).exists():
            raise serializers.ValidationError("Proposal already exists for this campaign.")

        proposal = Proposal.objects.create(**validated_data)
        proposal.categories.set(categories_data)

        for platform_data in platforms_data:
            ProposalPlatform.objects.create(proposal=proposal, **platform_data)

        for deliverable_data in deliverables_data:
            items_data = deliverable_data.pop('items', [])
            deliverable = ProposalDeliverable.objects.create(proposal=proposal, **deliverable_data)
            for item_data in items_data:
                ProposalDeliverableItem.objects.create(deliverable=deliverable, **item_data)

        for product_data in barter_products_data:
            CampaignProposalProduct.objects.create(proposal=proposal, **product_data)

        return proposal

    def update(self, instance, validated_data):
        platforms_data = validated_data.pop('platforms', None)
        deliverables_data = validated_data.pop('deliverables', None)
        categories_data = validated_data.pop('categories', None)
        barter_products_data = validated_data.pop('proposal_products', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if categories_data is not None:
            instance.categories.set(categories_data)

        if platforms_data is not None:
            instance.platforms.all().delete()
            for platform_data in platforms_data:
                ProposalPlatform.objects.create(proposal=instance, **platform_data)

        if deliverables_data is not None:
            instance.deliverables.all().delete()
            for deliverable_data in deliverables_data:
                items_data = deliverable_data.pop('items', [])
                deliverable = ProposalDeliverable.objects.create(proposal=instance, **deliverable_data)
                for item_data in items_data:
                    ProposalDeliverableItem.objects.create(deliverable=deliverable, **item_data)

        if barter_products_data is not None:
            instance.proposal_products.all().delete()
            for product_data in barter_products_data:
                CampaignProposalProduct.objects.create(proposal=instance, **product_data)

        return instance

class ProposalVersionDeliverableItemSerializer(serializers.ModelSerializer):
    platform = serializers.PrimaryKeyRelatedField(queryset=SocialMediaPlatform.objects.all())
    category = serializers.PrimaryKeyRelatedField(queryset=PlatformCategory.objects.all())

    class Meta:
        model = ProposalVersionDeliverableItem
        fields = ['platform', 'category', 'quantity', 'is_negotiable']
        read_only_fields = []  # Remove platform, category, quantity from read_only_fields


class ProposalVersionDeliverableSerializer(serializers.ModelSerializer):
    items = ProposalVersionDeliverableItemSerializer(many=True, required=False)

    class Meta:
        model = ProposalVersionDeliverable
        fields = ['name', 'items', 'is_negotiable']
        read_only_fields = ['name']  # Only is_negotiable can be modified


class ProposalVersionPlatformSerializer(serializers.ModelSerializer):
    platform = serializers.PrimaryKeyRelatedField(queryset=SocialMediaPlatform.objects.all())
    class Meta:
        model = ProposalVersionPlatform
        fields = ['platform']
        read_only_fields = []


class BarterProductVersionSerializer(serializers.ModelSerializer):

    image = serializers.CharField(required=False)
    class Meta:
        model = CampaignProposalVersionProduct
        fields = ['name', 'description', 'quantity_available', 'image', 'is_non_negotiable']

class ProposalVersionSerializer(serializers.ModelSerializer):
    platforms = ProposalVersionPlatformSerializer(many=True)
    platform_is_negotiable = serializers.BooleanField()
    deliverables = ProposalVersionDeliverableSerializer(many=True)
    sent_by_name = serializers.SerializerMethodField()
    sent_to_name = serializers.SerializerMethodField()
    talent_id = serializers.IntegerField(write_only=True, required=False)
    barter_products = BarterProductVersionSerializer(many=True)
    class Meta:
        model = ProposalVersion
        fields = [
            'id',
            'proposal',
            'version',
            'sent_by',
            'sent_by_name',
            'sent_to',
            'sent_to_name',
            'status',
            'duration',
            'duration_type',
            'offer_amount_min',
            'offer_amount_max',
            'offer_amount',
            'comments',
            'platform_is_negotiable',
            'platforms',
            'deliverables',
            'created_at',
            'talent_id',
            'barter_products'
        ]
        read_only_fields = ['version', 'sent_by', 'sent_by_name', 'sent_to', 'sent_to_name', 'status']

    def get_sent_by_name(self, obj):
        return obj.sent_by.get_full_name() or obj.sent_by.email

    def get_sent_to_name(self, obj):
        return obj.sent_to.get_full_name() or obj.sent_to.email

    def validate(self, data):
        # Get the current user
        request = self.context.get('request')
        if not request or not request.user:
            raise serializers.ValidationError("User context is required")

        user = request.user
        proposal = data['proposal']
        is_brand = (proposal.campaign.brand == user)

        # Get the latest version to copy non-negotiable fields
        latest_version = ProposalVersion.objects.filter(
            proposal=proposal
        ).order_by('-version').first()

        if latest_version:
            # Copy non-negotiable fields from latest version
            if not is_brand:
                # For talent, copy all fields except offer_amount and comments
                data['duration'] = latest_version.duration
                data['duration_type'] = latest_version.duration_type
                
                # If platforms not provided, copy from latest version
                if 'platforms' not in data:
                    data['platforms'] = [{
                        'platform': platform.platform,
                        'is_negotiable': True
                    } for platform in latest_version.platforms.all()]
                
                # If deliverables not provided, copy from latest version
                if 'deliverables' not in data:
                    data['deliverables'] = []
                    for deliverable in latest_version.deliverables.all():
                        items = [{
                            'platform': item.platform,
                            'category': item.category,
                            'quantity': item.quantity,
                            'is_negotiable': True
                        } for item in deliverable.items.all()]
                        
                        data['deliverables'].append({
                            'name': deliverable.name,
                            'is_negotiable': True,
                            'items': items
                        })

        # Set sent_by and sent_to
        data['sent_by'] = user
        if is_brand:
            # Brand is sending to talent
            talent_id = self.initial_data.get('talent_id')
            if talent_id:
                talent_application = proposal.campaign.applications.filter(talent__id=talent_id).first()
                if talent_application:
                    data['sent_to'] = talent_application.talent.user
                else:
                    raise serializers.ValidationError({"talent_id": "Invalid talent_id - talent has not applied to this campaign"})
            else:
                raise serializers.ValidationError({"talent_id": "talent_id is required when brand creates a proposal version"})
        else:
            # Talent is sending to brand
            data['sent_to'] = proposal.campaign.brand

        return data

    def create(self, validated_data):
        try:
            platforms_data = validated_data.pop('platforms', [])
            deliverables_data = validated_data.pop('deliverables', [])
            barter_products_data = validated_data.pop('barter_products', [])
            
            # Remove talent_id if it exists in validated_data
            validated_data.pop('talent_id', None)

            # Get the latest version to copy items
            latest_version = ProposalVersion.objects.filter(
                proposal=validated_data['proposal']
            ).order_by('-version').first()
            
            with transaction.atomic():
                # Calculate new version
                new_version = float(latest_version.version) + 0.1 if latest_version else 1.0
                validated_data['version'] = round(new_version, 1)

                # Create the proposal version
                proposal_version = ProposalVersion.objects.create(**validated_data)

                # For first version, create from provided data
                if not latest_version:
                    try:
                        # Create platforms
                        for platform_data in platforms_data:
                            platform = platform_data.get('platform')
                            if not platform:
                                raise serializers.ValidationError("Platform ID is required")
                            
                            ProposalVersionPlatform.objects.create(
                                proposal_version=proposal_version,
                                platform_id=platform.id if hasattr(platform, 'id') else platform,
                                # is_negotiable=platform_data.get('is_negotiable', False)
                            )

                        # Create deliverables
                        for deliverable_data in deliverables_data:
                            items_data = deliverable_data.pop('items', [])
                            deliverable = ProposalVersionDeliverable.objects.create(
                                proposal_version=proposal_version,
                                **deliverable_data
                            )
                            
                            # Create items
                            for item_data in items_data:
                                platform = item_data.get('platform')
                                category = item_data.get('category')
                                
                                if not platform:
                                    raise serializers.ValidationError("Platform ID is required for deliverable item")
                                if not category:
                                    raise serializers.ValidationError("Category ID is required for deliverable item")
                                
                                ProposalVersionDeliverableItem.objects.create(
                                    deliverable=deliverable,
                                    platform_id=platform.id if hasattr(platform, 'id') else platform,
                                    category_id=category.id if hasattr(category, 'id') else category,
                                    quantity=item_data.get('quantity', 1),
                                    is_negotiable=item_data.get('is_negotiable', False)
                                )

                        # Save barter products
                        for product in barter_products_data:
                            CampaignProposalVersionProduct.objects.create(
                                proposal_version=proposal_version,
                                name=product.get("name"),
                                description=product.get("description"),
                                image=product.get("image"),
                                quantity_available=product.get("quantity_available", 1),
                                is_non_negotiable=product.get("is_non_negotiable", False)
                            )
                    except Exception as e:
                        raise serializers.ValidationError(f"Error creating first version: {str(e)}")
                else:
                    # Copy from latest version
                    try:
                        for platform in latest_version.platforms.all():
                            ProposalVersionPlatform.objects.create(
                                proposal_version=proposal_version,
                                platform=platform.platform,
                                # is_negotiable=True
                            )

                        for deliverable in latest_version.deliverables.all():
                            new_deliverable = ProposalVersionDeliverable.objects.create(
                                proposal_version=proposal_version,
                                name=deliverable.name,
                                is_negotiable=True
                            )
                            
                            for item in deliverable.items.all():
                                ProposalVersionDeliverableItem.objects.create(
                                    deliverable=new_deliverable,
                                    platform=item.platform,
                                    category=item.category,
                                    quantity=item.quantity,
                                    is_negotiable=True
                                )

                        #  Copy barter products
                        for barter_product in latest_version.barter_products.all():
                            CampaignProposalVersionProduct.objects.create(
                                proposal_version=proposal_version,
                                name=barter_product.name,
                                description=barter_product.description,
                                image=barter_product.image,
                                quantity_available=barter_product.quantity_available,
                                is_non_negotiable=True  
                            )
                    except Exception as e:
                        raise serializers.ValidationError(f"Error copying from latest version: {str(e)}")

                return proposal_version

        except Exception as e:
            raise serializers.ValidationError(f"Failed to create proposal version: {str(e)}")
        

class CampaignBookmarkSerializer(serializers.ModelSerializer):

    campaign = HelperCampaignSerializer(read_only=True)
    status = OptionChoiceField(choices=ApplicationStatusChoices.choices,default=ApplicationStatusChoices.APPLIED,)


    class Meta:
        model = CampaignBookmark
        fields = "__all__"
        read_only_fields = ['id', 'created_at', 'talent']

class RoomSerializer(serializers.ModelSerializer):
    """Serializer for Room model"""
    campaign_name = serializers.CharField(source='campaign.campaign_name', read_only=True)
    participants = serializers.SerializerMethodField()
    
    class Meta:
        model = Room
        fields = ['id', 'name', 'campaign', 'campaign_name', 'is_active', 'last_message_at', 'participants']
        read_only_fields = ['id', 'campaign_name', 'participants']

    def get_participants(self, obj):
        participants = obj.participants.filter(is_active=True)
        return RoomParticipantSerializer(participants, many=True).data

class RoomParticipantSerializer(serializers.ModelSerializer):
    """Serializer for RoomParticipant model"""
    user_email = serializers.EmailField(source='user.email', read_only=True)
    user_name = serializers.SerializerMethodField()
    
    class Meta:
        model = RoomParticipant
        fields = ['id', 'room', 'user', 'user_email', 'user_name', 'role', 'is_active', 'last_read_at']
        read_only_fields = ['id', 'user_email', 'user_name']

    def get_user_name(self, obj):
        if obj.role == 'brand':
            try:
                return obj.user.brand_profile.brand_name
            except:
                return obj.user.email
        else:
            try:
                return obj.user.talent_profile.name
            except:
                return obj.user.email

class GreetingMessageSerializer(serializers.Serializer):
    talent_id = serializers.IntegerField(required=True)

    def validate_talent_id(self, value):
        try:
            talent = User.objects.get(id=value, user_type='T')
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("Talent not found")

    def get_greeting_messages(self, talent_name):
        return [
            {
                "id": "connect",
                "message": f"Hi {talent_name}, great to connect with you!"
            },
            {
                "id": "wellbeing",
                "message": f"Hello {talent_name}, hope you're doing well."
            },
            {
                "id": "opportunity",
                "message": f"Hi {talent_name}, let's discuss the campaign opportunity."
            }
        ]
    

class ActiveCampaignProposalSerializer(serializers.ModelSerializer):
    categories = serializers.StringRelatedField(many=True)
    campaign_id = serializers.IntegerField(source='campaign.id')
    campaign_name = serializers.CharField(source='campaign.campaign_name')
    

    class Meta:
        model = Proposal
        fields = [
            'campaign_id', 'campaign_name',
            'duration', 'duration_type', 'duration_is_negotiable',
            'categories', 'deliverable_is_negotiable',
            'initial_offer_min', 'initial_offer_max',
            'offer_is_negotiable'
        ]

class ChecklistExtraItemSerializer(serializers.ModelSerializer):
    """Serializer for ChecklistExtraItem model"""
    
    class Meta:
        model = ChecklistExtraItem
        fields = ['id', 'text', 'checklist_media']
        read_only_fields = ['id']


class ChecklistItemSerializer(serializers.ModelSerializer):
    """Serializer for ChecklistItem model"""
    extra_checklist = ChecklistExtraItemSerializer(many=True, required=False)
    deliverable_name = serializers.CharField(source='deliverable.name', read_only=True)
    campaign_name = serializers.CharField(source='deliverable.proposal_version.proposal.campaign.campaign_name', read_only=True)
    brand_name = serializers.CharField(source='deliverable.proposal_version.proposal.campaign.brand.email', read_only=True)
    
    class Meta:
        model = ChecklistItem
        fields = [
            'id', 'deliverable', 'deliverable_name', 'campaign_name', 'brand_name',
            'outfit', 'background', 'sound_quality', 'lighting', 
            'camera_orientation', 'editing', 'script_breakdown', 'extra_checklist'
        ]
        read_only_fields = ['id', 'deliverable_name', 'campaign_name', 'brand_name']

    def validate_deliverable(self, value):
        # Ensure the deliverable belongs to a proposal that the user has access to
        request = self.context.get('request')
        if request and request.user:
            proposal_version = value.proposal_version
            proposal = proposal_version.proposal
            campaign = proposal.campaign
            
            # Brand can access if they own the campaign
            if hasattr(request.user, 'brand_profile') and campaign.brand == request.user:
                return value
            
            # Talent can access if they are assigned to this deliverable
            # This would need to be implemented based on your assignment logic
            # For now, allowing talent to access if they have a talent profile
            if hasattr(request.user, 'talent_profile'):
                return value
                
            raise serializers.ValidationError("You don't have permission to access this deliverable")
        return value

    def create(self, validated_data):
        # Extract extra_checklist data
        extra_checklist_data = validated_data.pop('extra_checklist', [])
        
        # Only brands can create checklists
        request = self.context.get('request')
        if request and request.user:
            deliverable = validated_data['deliverable']
            proposal_version = deliverable.proposal_version
            proposal = proposal_version.proposal
            campaign = proposal.campaign
            
            if not (hasattr(request.user, 'brand_profile') and campaign.brand == request.user):
                raise serializers.ValidationError("Only brands can create checklists")
        
        # Create the checklist item
        checklist_item = super().create(validated_data)
        
        # Create extra checklist items
        for extra_item_data in extra_checklist_data:
            ChecklistExtraItem.objects.create(checklist=checklist_item, **extra_item_data)
        
        return checklist_item

    def update(self, instance, validated_data):
        # Extract extra_checklist data
        extra_checklist_data = validated_data.pop('extra_checklist', [])
        
        # Both brands and talents can update checklists
        request = self.context.get('request')
        if request and request.user:
            deliverable = instance.deliverable
            proposal_version = deliverable.proposal_version
            proposal = proposal_version.proposal
            campaign = proposal.campaign
            
            # Brand can update if they own the campaign
            if hasattr(request.user, 'brand_profile') and campaign.brand == request.user:
                # Update the checklist item
                checklist_item = super().update(instance, validated_data)
                
                # Handle extra checklist items
                if extra_checklist_data is not None:  # Only update if provided
                    # Clear existing extra items and create new ones
                    instance.extra_checklist.all().delete()
                    for extra_item_data in extra_checklist_data:
                        ChecklistExtraItem.objects.create(checklist=checklist_item, **extra_item_data)
                
                return checklist_item
            
            # Talent can update if they have a talent profile
            if hasattr(request.user, 'talent_profile'):
                # Update the checklist item
                checklist_item = super().update(instance, validated_data)
                
                # Handle extra checklist items
                if extra_checklist_data is not None:  # Only update if provided
                    # Clear existing extra items and create new ones
                    instance.extra_checklist.all().delete()
                    for extra_item_data in extra_checklist_data:
                        ChecklistExtraItem.objects.create(checklist=checklist_item, **extra_item_data)
                
                return checklist_item
                
            raise serializers.ValidationError("You don't have permission to update this checklist")
        
        return super().update(instance, validated_data)

class CampaignProductSerializer(serializers.ModelSerializer):
    """Serializer for campaign products (Step 6)"""
    image = serializers.CharField(required=False)
    
    class Meta:
        model = CampaignProduct
        fields = ['name', 'description', 'image', 'quantity_available']

    def validate_name(self, value):
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Product name must be at least 2 characters long")
        return value.strip()

    def validate_quantity_available(self, value):
        if value < 1:
            raise serializers.ValidationError("Quantity must be at least 1")
        return value

    def validate(self, data):
        # Check if this is part of a campaign creation/update
        campaign = self.context.get('campaign')
        if campaign:
            # Check if we're not exceeding the maximum of 5 products
            existing_count = CampaignProduct.objects.filter(campaign=campaign).count()
            if self.instance:  # Update case
                existing_count -= 1  # Exclude current instance
            
            if existing_count >= 5:
                raise serializers.ValidationError("Maximum 5 products allowed per campaign")
        
        return data


class ScriptSerializer(serializers.ModelSerializer):
    talent_id = serializers.IntegerField(required=False)
    campaign_name = serializers.SerializerMethodField()
    sent_by_name = serializers.SerializerMethodField()
    sent_to_name = serializers.SerializerMethodField()
    campaign_id = serializers.SerializerMethodField()

    class Meta:
        model = Script
        fields = [
            'id', 'proposal_version', 'campaign_id', 'campaign_name',
            'content', 'talent_id', 'feedback',
            'sent_by', 'sent_by_name',
            'sent_to', 'sent_to_name', 'status',
            'version', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'sent_by', 'sent_to', 'version']

    def get_campaign_id(self, obj):
        return obj.proposal_version.proposal.campaign.id if obj.proposal_version and obj.proposal_version.proposal and obj.proposal_version.proposal.campaign else None

    def get_campaign_name(self, obj):
        return obj.proposal_version.proposal.campaign.campaign_name if obj.proposal_version and obj.proposal_version.proposal and obj.proposal_version.proposal.campaign else None

    def get_sent_by_name(self, obj):
        user = obj.sent_by
        if not user:
            return None
        if user.user_type == UserTypeChoices.BRAND.value[0]:
            return getattr(user.brand_profile, "brand_admin_name", None)
        elif user.user_type == UserTypeChoices.TALENT.value[0]:
            return getattr(user.talent_profile, "name", None)
        return None

    def get_sent_to_name(self, obj):
        user = obj.sent_to
        if not user:
            return None
        if user.user_type == UserTypeChoices.BRAND.value[0]:
            return getattr(user.brand_profile, "brand_admin_name", None)
        elif user.user_type == UserTypeChoices.TALENT.value[0]:
            return getattr(user.talent_profile, "name", None)
        return None

    def create(self, validated_data):
        request = self.context.get("request")
        user = request.user
        proposal_version = validated_data.get("proposal_version")
        content = validated_data.get("content")
        feedback = validated_data.get("feedback")
        user_type = getattr(user, "user_type", "")
        talent_id = validated_data.pop("talent_id", None)

        if user_type == UserTypeChoices.BRAND.value[0]:
            if not talent_id:
                raise serializers.ValidationError({
                    "talent_id": "This field is required when brand is sending script."
                })
            try:
                talent_profile = TalentProfile.objects.get(id=talent_id)
            except TalentProfile.DoesNotExist:
                raise serializers.ValidationError({
                    "talent_id": "Talent profile not found."
                })
            sent_to_user = talent_profile.user
        elif user_type == UserTypeChoices.TALENT.value[0]:
            if not proposal_version or not proposal_version.proposal or not proposal_version.proposal.campaign or not proposal_version.proposal.campaign.brand:
                raise serializers.ValidationError({
                    "proposal_version": "Invalid proposal version or missing campaign brand."
                })
            sent_to_user = proposal_version.proposal.campaign.brand
        else:
            raise serializers.ValidationError({"user_type": "Unsupported user type."})

        latest_version = Script.objects.filter(proposal_version=proposal_version).order_by('-version').first()
        new_version = round(float(latest_version.version) + 0.1, 1) if latest_version else 1.0
        validated_data['version'] = new_version

        return Script.objects.create(
            proposal_version=proposal_version,
            content=content,
            feedback=feedback,
            sent_by=user,
            sent_to=sent_to_user,
            version=new_version
        )

class ContentUploadSerializer(serializers.ModelSerializer):
    """
    Serializer for ContentUpload model
    Handles content upload workflow: Talent uploads → Brand reviews → Approval/Rejection
    """
    sent_by_name = serializers.CharField(source='sent_by.email', read_only=True)
    sent_to_name = serializers.CharField(source='sent_to.email', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.email', read_only=True)
    rejected_by_name = serializers.CharField(source='rejected_by.email', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    deliverable_details = serializers.SerializerMethodField()
    script_details = serializers.SerializerMethodField()
    
    class Meta:
        model = ContentUpload
        fields = [
            'id', 'script', 'script_details', 'deliverable', 'deliverable_details',
            'upload_content', 'link', 'version', 'status', 'status_display',
            'feedback', 'sent_by', 'sent_by_name', 'sent_to', 'sent_to_name',
            'approved_by', 'approved_by_name', 'rejected_by', 'rejected_by_name',
            'room', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'version', 'sent_by_name', 'sent_to_name',
            'approved_by', 'approved_by_name', 'rejected_by', 'rejected_by_name',
            'created_at', 'updated_at'
        ]

    def update(self, instance, validated_data):
        """Handle partial updates for content upload"""
        user = self.context.get('request').user if self.context.get('request') else None
        
        # Handle status updates by brands
        if 'status' in validated_data and hasattr(user, 'brand_profile'):
            new_status = validated_data['status']
            
            # Update status and set appropriate user fields
            instance.status = new_status
            if new_status == ContentUploadStatus.FINALISED:
                instance.approved_by = user
                instance.rejected_by = None
            elif new_status == ContentUploadStatus.REJECTED:
                instance.rejected_by = user
                instance.approved_by = None
            else:
                instance.approved_by = None
                instance.rejected_by = None
        
        # Handle content updates by talents
        if hasattr(user, 'talent_profile'):
            # Update content fields
            for field in ['upload_content', 'link', 'feedback']:
                if field in validated_data:
                    setattr(instance, field, validated_data[field])
            
            # If updating rejected content with new content, reset status to SENT
            if instance.status == ContentUploadStatus.REJECTED and ('upload_content' in validated_data or 'link' in validated_data):
                instance.status = ContentUploadStatus.SENT
                instance.rejected_by = None  # Clear rejection info for new version
        
        # Handle feedback updates by brands
        elif hasattr(user, 'brand_profile') and 'feedback' in validated_data:
            instance.feedback = validated_data['feedback']
        
        instance.save()
        return instance

    def get_deliverable_details(self, obj):
        """Get deliverable details for display"""
        return {
            'id': obj.deliverable.id,
            'name': obj.deliverable.deliverable.name,
            'platform': obj.deliverable.platform.name,
            'category': obj.deliverable.category.name,
            'quantity': obj.deliverable.quantity
        }

    def get_script_details(self, obj):
        """Get script details for display"""
        return {
            'id': obj.script.id,
            'version': obj.script.version,
            'content': obj.script.content[:100] + '...' if obj.script.content and len(obj.script.content) > 100 else obj.script.content
        }

    def validate_upload_content(self, value):
        """Validate upload content (max 4 items)"""
        if value and len(value) > 4:
            raise serializers.ValidationError("Maximum 4 content items allowed")
        return value

    def validate(self, data):
        """Additional validation for content upload"""
        # Ensure either upload_content or link is provided
        upload_content = data.get('upload_content', [])
        link = data.get('link')
        
        if not upload_content and not link:
            raise serializers.ValidationError("Either upload_content or link must be provided")
        
        # Validate content count
        if upload_content and len(upload_content) > 4:
            raise serializers.ValidationError("Maximum 4 content items allowed")
        
        return data

    def create(self, validated_data):
        """Create content upload with auto versioning"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['sent_by'] = request.user
        
        # Use the service to create content upload
        from .services import ContentUploadService
        return ContentUploadService.create_content_upload(
            script=validated_data['script'],
            deliverable=validated_data['deliverable'],
            upload_content=validated_data.get('upload_content', []),
            sent_by=validated_data['sent_by'],
            sent_to=validated_data['sent_to'],
            room=validated_data.get('room'),
            link=validated_data.get('link')
        )





class ContentUploadHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for content upload history
    """
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    content_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ContentUpload
        fields = [
            'id', 'version', 'status', 'status_display', 'content_count',
            'feedback', 'created_at'
        ]
    
    def get_content_count(self, obj):
        return obj.content_count


class ProductDeliverySerializer(serializers.ModelSerializer):
    """
    ProductDelivery serializer
    """
    talent_email = serializers.EmailField(source='talent.email', read_only=True)
    brand_email = serializers.EmailField(source='brand.email', read_only=True)
    can_confirm_receipt = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = ProductDelivery
        fields = [
            'id', 'delivery_address', 'expected_delivery_date', 'receipt_status',
            'last_receipt_check', 'next_check_allowed', 'can_confirm_receipt',
            'talent_email', 'brand_email', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_receipt_check', 'next_check_allowed', 'can_confirm_receipt',
            'talent_email', 'brand_email', 'created_at', 'updated_at'
        ]

    def validate_delivery_address(self, value):
        if value and len(value.strip()) < 10:
            raise serializers.ValidationError("Delivery address must be at least 10 characters long")
        return value.strip() if value else value

    def validate_expected_delivery_date(self, value):
        if value and value <= timezone.now().date():
            raise serializers.ValidationError("Expected delivery date must be in the future")
        return value

class CampaignReviewSerializer(serializers.ModelSerializer):
    reviewer_email = serializers.EmailField(source='reviewer.email', read_only=True)
    reviewee_email = serializers.EmailField(source='reviewee.email', read_only=True)
    campaign_name = serializers.CharField(source='campaign.campaign_name', read_only=True)

    class Meta:
        model = CampaignReview
        fields = ['id', 'campaign', 'campaign_name', 'reviewer', 'reviewer_email', 'reviewee', 'reviewee_email', 'rating', 'comment', 'created_at']
        read_only_fields = ['id', 'created_at', 'reviewer', 'reviewer_email', 'reviewee_email', 'campaign_name']
