import django_filters

from accounts.choices import GenderChoices
from .models import TalentCampaignApplication, ApplicationStatusChoices

class TalentCampaignApplicationFilter(django_filters.FilterSet):
    status = django_filters.ChoiceFilter(choices=ApplicationStatusChoices.choices)
    campaign_min_budget = django_filters.NumberFilter(field_name='campaign__min_budget', lookup_expr='gte')
    campaign_max_budget = django_filters.NumberFilter(field_name='campaign__max_budget', lookup_expr='lte')
    campaign_state = django_filters.CharFilter(field_name='campaign__preferred_state', lookup_expr='icontains')
    campaign_city = django_filters.CharFilter(field_name='campaign__preferred_city', lookup_expr='icontains')


    # Multiple state filter
    campaign_state = django_filters.CharFilter(method='filter_state')
    # Multiple city filter
    campaign_city = django_filters.Char<PERSON>ilter(method='filter_city')
    # Multiple gender filter
    campaign_gender = django_filters.Char<PERSON>ilter(method='filter_gender')
    # Multiple category filter
    campaign_category = django_filters.CharFilter(method='filter_category')

    class Meta:
        model = TalentCampaignApplication
        fields = ['status']

    def filter_state(self, queryset, name, value):
        state_list = [state.strip() for state in value.split(',') if state.strip()]
        if state_list:
            queryset = queryset.filter(campaign__preferred_state__in=state_list)
        return queryset

    def filter_city(self, queryset, name, value):
        city_list = [city.strip() for city in value.split(',') if city.strip()]
        if city_list:
            queryset = queryset.filter(campaign__preferred_city__in=city_list)
        return queryset

    def filter_gender(self, queryset, name, value):
        gender_choices = [choice[0] for choice in GenderChoices.get_choices()]
        gender_list = [g.strip() for g in value.split(',') if g.strip() in gender_choices]
        if gender_list:
            queryset = queryset.filter(campaign__preferred_gender__in=gender_list)
        return queryset

    def filter_category(self, queryset, name, value):
        try:
            category_ids = [int(cid.strip()) for cid in value.split(',') if cid.strip().isdigit()]
            if category_ids:
                queryset = queryset.filter(campaign__category__in=category_ids)
        except Exception:
            return queryset.none()
        return queryset


    class Meta:
        model = TalentCampaignApplication
        fields = ['status']
