from django.db import models
from accounts.models import Language, PlatformCategory, SocialMediaPlatform, TalentProfile, User
from accounts.models import BaseModel,ExpertiseArea
from accounts.choices import DurationTypeChoices, GenderChoices
from talent_trade.utils.choices import ApplicationStatusChoices, CampaignStatusChoices, CampaignTypeChoices, ScriptStatus,ContentUploadStatus
from django.core.validators import MinValueValidator
from decimal import Decimal
from django.contrib.postgres.fields import J<PERSON>NField 

class Campaign(BaseModel):
    brand = models.ForeignKey(User, on_delete=models.CASCADE, related_name='campaigns')
    campaign_name = models.CharField(max_length=255)
    brief = models.TextField(max_length=3000, null=True)
    hashtags = models.CharField(max_length=512, help_text="Comma-separated hashtags",null=True,blank=True)
    final_submission_date = models.DateField()
    preferred_state = models.CharField(max_length=100, null=True, blank=True)
    preferred_city = models.CharField(max_length=100, null=True, blank=True)
    preferred_languages = models.JSONField(default=list, null=True, blank=True)  
    min_budget = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)  
    max_budget = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    status = models.IntegerField(choices=CampaignStatusChoices.choices, default=CampaignStatusChoices.DRAFT)
    languages = models.ManyToManyField(Language, related_name='campaign_language', blank=True)
    campaign_banner = models.ImageField(upload_to="campaign_banners/",null=True,blank=True)
    preferred_gender = models.JSONField(default=list, null=True, blank=True)
    category = models.ManyToManyField(ExpertiseArea,related_name='campaigns_expertise',blank=True)
    campaign_type = models.CharField(
        max_length=10,
        choices=CampaignTypeChoices,
        default=CampaignTypeChoices.PAID
    )


    def __str__(self):
        return self.campaign_name


class CampaignProduct(BaseModel):
    """
    Products that brands can add to their campaigns for delivery
    Step 6 of campaign creation
    """
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='campaign_products')
    name = models.CharField(max_length=255, help_text="Product name")
    description = models.TextField(max_length=1000, blank=True, null=True, help_text="Product description")
    image = models.ImageField(upload_to="campaign_product_images/", null=True, blank=True)
    cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Cost of the product for this campaign"
    )
    is_product_available = models.BooleanField(null=True, blank=True)

    class Meta:
        verbose_name = 'Campaign Product'
        verbose_name_plural = 'Campaign Products'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.campaign.campaign_name}"


class CampaignPlatformSelection(BaseModel):
    brand = models.ForeignKey(User, on_delete=models.CASCADE, related_name='campaign_platform_selections',null=True,blank=True)  # ✅ added
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='platform_selections')
    platform = models.ForeignKey(SocialMediaPlatform, on_delete=models.CASCADE, related_name='campaign_platforms')

    def __str__(self):
        return self.platform.name


class CampaignDeliverable(BaseModel):
    brand = models.ForeignKey(User, on_delete=models.CASCADE, related_name='campaign_deliverables',null=True,blank=True)
    platform_selection = models.ForeignKey(CampaignPlatformSelection, on_delete=models.CASCADE, related_name='deliverables')
    category_type = models.ForeignKey(PlatformCategory, on_delete=models.CASCADE, related_name='campaign_deliverables')
    count = models.PositiveIntegerField(default=1)

    def __str__(self):
        return self.category_type.name


class CampaignGoLiveDate(BaseModel):
    brand = models.ForeignKey(User, on_delete=models.CASCADE, related_name='campaign_go_live_dates',null=True,blank=True)
    deliverable = models.ForeignKey(CampaignDeliverable, on_delete=models.CASCADE, related_name='go_live_dates') 
    go_live_date = models.DateField()
    is_active = models.BooleanField(default=False)

    # @property
    # def talent_user(self):
    #     return self.talent.user  # TalentProfile --> User
    #     # def __str__(self):
    #     #     return self.deliverable


class TalentCampaignApplication(BaseModel):

    talent = models.ForeignKey(TalentProfile, on_delete=models.CASCADE, related_name='applications')
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='applications')
    status = models.IntegerField(choices=ApplicationStatusChoices.choices, default=ApplicationStatusChoices.APPLIED)

    @property
    def talent_user(self):
        return self.talent.user

    class Meta:
        unique_together = ('talent', 'campaign')

class Proposal(BaseModel):
    campaign = models.OneToOneField(Campaign, on_delete=models.CASCADE, db_index=True)
    duration = models.PositiveIntegerField()
    duration_type = models.CharField(
        max_length=10,
        choices=DurationTypeChoices.get_choices(),
        default=DurationTypeChoices.MONTHS.value[0]
    )
    duration_is_negotiable = models.BooleanField(default=False)
    categories = models.ManyToManyField(ExpertiseArea, related_name='proposals')
    deliverable_is_negotiable = models.BooleanField(default=False)
    initial_offer_min = models.DecimalField(max_digits=10, decimal_places=2)
    initial_offer_max = models.DecimalField(max_digits=10, decimal_places=2)
    offer_is_negotiable = models.BooleanField(default=False)
    platform_is_negotiable = models.BooleanField(default=False)

    def __str__(self):
        return f"Proposal for Campaign: {self.campaign.campaign_name}"


class ProposalPlatform(BaseModel):
    proposal = models.ForeignKey(Proposal, related_name="platforms", on_delete=models.CASCADE, db_index=True)
    platform = models.ForeignKey(SocialMediaPlatform, on_delete=models.CASCADE, db_index=True)
    def __str__(self):
        return f"{self.platform.name} (Proposal ID: {self.proposal.id})"


class ProposalDeliverable(BaseModel):
    proposal = models.ForeignKey(Proposal, related_name="deliverables", on_delete=models.CASCADE, db_index=True)
    name = models.CharField(max_length=100)  # e.g., Deliverable 1

    def __str__(self):
        return f"{self.name} (Proposal ID: {self.proposal.id})"


class ProposalDeliverableItem(BaseModel):
    deliverable = models.ForeignKey(ProposalDeliverable, related_name="items", on_delete=models.CASCADE, db_index=True)
    platform = models.ForeignKey(SocialMediaPlatform, on_delete=models.CASCADE, db_index=True)
    category = models.ForeignKey(PlatformCategory, on_delete=models.CASCADE, db_index=True)
    quantity = models.IntegerField()

    def __str__(self):
        return f"{self.quantity} x {self.category.name} on {self.platform.name} (Deliverable: {self.deliverable.name})"



class CampaignProposalProduct(BaseModel):
    """
    Products that brands can add to their campaign proposals
    """
    proposal = models.ForeignKey(Proposal, on_delete=models.CASCADE, related_name='proposal_products')
    name = models.CharField(max_length=255, help_text="Product name")
    description = models.TextField(max_length=1000, blank=True, null=True, help_text="Product description")
    
    quantity_available = models.PositiveIntegerField(
        default=1,
        help_text="Number of products available for this campaign"
    )
    image = models.ImageField(upload_to="barter_product_images/",null=True,blank=True)
    is_non_negotiable = models.BooleanField(default=False, help_text="Mark if the product price/availability is non-negotiable")

    class Meta:
        verbose_name = 'Campaign Product'
        verbose_name_plural = 'Campaign Products'
        ordering = ['-created_at']
       
class ProposalNegotiationStatus(models.TextChoices):
    SENT = 'SENT', 'Sent'
    ACCEPTED = 'ACCEPTED', 'Accepted'
    TERMINATED = 'TERMINATED', 'Terminated'

class ProposalVersion(BaseModel):
    """
    Tracks different versions of a proposal during negotiation
    """
    proposal = models.ForeignKey(Proposal, on_delete=models.CASCADE, related_name='versions')
    version = models.FloatField(help_text="Version number (e.g., 1.0, 1.1, 1.2)")
    sent_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_proposals')
    sent_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_proposals')
    status = models.CharField(
        max_length=20,
        choices=ProposalNegotiationStatus.choices,
        default=ProposalNegotiationStatus.SENT
    )
    # Negotiable fields
    duration = models.PositiveIntegerField()
    duration_type = models.CharField(
        max_length=10,
        choices=DurationTypeChoices.get_choices(),
        default=DurationTypeChoices.MONTHS
    )
    offer_amount_min = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], null=True, blank=True)
    offer_amount_max = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], null=True, blank=True)
    offer_amount = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        null=True,
        blank=True
    )
    # Comments for negotiation
    comments = models.TextField(blank=True, null=True, help_text="Comments or notes for this version")
    platform_is_negotiable = models.BooleanField(default=False)
    class Meta:
        ordering = ['-version']
        indexes = [
            models.Index(fields=['proposal', '-version']),
            models.Index(fields=['sent_by', '-created_at']),
            models.Index(fields=['sent_to', '-created_at']),
            models.Index(fields=['status']),
        ]
        unique_together = ['proposal', 'version']
    def __str__(self):
        return f"Proposal {self.proposal.id} - v{self.version} ({self.status})"
    def save(self, *args, **kwargs):
        # If this is a new version, calculate the next version number
        if not self.pk and not self.version:
            latest_version = ProposalVersion.objects.filter(
                proposal=self.proposal
            ).order_by('-version').first()
            self.version = round(float(latest_version.version) + 0.1, 1) if latest_version else 1.0
        super().save(*args, **kwargs)

class ProposalVersionPlatform(BaseModel):
    """
    Platforms included in each proposal version
    """
    proposal_version = models.ForeignKey(ProposalVersion, related_name="platforms", on_delete=models.CASCADE)
    platform = models.ForeignKey(SocialMediaPlatform, on_delete=models.CASCADE)
    class Meta:
        unique_together = ['proposal_version', 'platform']
    def __str__(self):
        return f"{self.platform.name} (Version: {self.proposal_version.version})"


class ProposalVersionDeliverable(BaseModel):
    """
    Deliverables for each proposal version
    """
    proposal_version = models.ForeignKey(ProposalVersion, related_name="deliverables", on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    is_negotiable = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.name} (Version: {self.proposal_version.version})"


class ProposalVersionDeliverableItem(BaseModel):
    """
    Individual items within each deliverable for a proposal version
    """
    deliverable = models.ForeignKey(ProposalVersionDeliverable, related_name="items", on_delete=models.CASCADE)
    platform = models.ForeignKey(SocialMediaPlatform, on_delete=models.CASCADE)
    category = models.ForeignKey(PlatformCategory, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    is_negotiable = models.BooleanField(default=False)

    class Meta:
        unique_together = ['deliverable', 'platform', 'category']

    def __str__(self):
        return f"{self.quantity} x {self.category.name} on {self.platform.name}"


class CampaignProposalVersionProduct(BaseModel):
    """
    Products that brands can add to their campaign proposals
    """
    proposal_version = models.ForeignKey(ProposalVersion, related_name="barter_products", on_delete=models.CASCADE)
    name = models.CharField(max_length=255, help_text="Product name")
    description = models.TextField(max_length=1000, blank=True, null=True, help_text="Product description")
    
    quantity_available = models.PositiveIntegerField(
        default=1,
        help_text="Number of products available for this campaign"
    )
    image = models.ImageField(upload_to="barter_product_images/",null=True,blank=True)
    is_non_negotiable = models.BooleanField(default=False, help_text="Mark if the product price/availability is non-negotiable")

    class Meta:
        verbose_name = 'Campaign Product'
        verbose_name_plural = 'Campaign Products'
        ordering = ['-created_at']
       


class CampaignBookmark(BaseModel):
    """
    Represents a bookmarked campaign by a talent user
    """
    talent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bookmarked_campaigns'
    )
    campaign = models.ForeignKey(
        Campaign,
        on_delete=models.CASCADE,
        related_name='bookmarks'
    )

    class Meta:
        ordering = ['-created_at']

class Room(BaseModel):
    """
    Represents a chat room between a brand and talent
    """
    name = models.CharField(max_length=255)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='rooms')
    is_active = models.BooleanField(default=True)
    last_message_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'Room'
        verbose_name_plural = 'Rooms'
        indexes = [
            models.Index(fields=['campaign']),
            models.Index(fields=['is_active']),
            models.Index(fields=['last_message_at']),
        ]

    def __str__(self):
        return self.name

class RoomParticipant(BaseModel):
    """
    Represents participants (brand and talent) in a room
    """
    ROLE_CHOICES = [
        ('brand', 'Brand'),
        ('talent', 'Talent'),
    ]

    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='room_participations')
    role = models.CharField(max_length=10, choices=ROLE_CHOICES)
    is_active = models.BooleanField(default=True)
    last_read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'Room Participant'
        verbose_name_plural = 'Room Participants'
        unique_together = ('room', 'user')  # Ensure one user can only be in a room once
        indexes = [
            models.Index(fields=['room']),
            models.Index(fields=['user']),
            models.Index(fields=['role']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.role} in {self.room.name}"

class ChecklistItem(BaseModel):
    deliverable = models.ForeignKey(ProposalVersionDeliverable, on_delete=models.CASCADE, related_name='checklist')

    outfit = models.CharField(max_length=255, blank=True, null=True)
    background = models.CharField(max_length=255, blank=True, null=True)
    sound_quality = models.CharField(max_length=255, blank=True, null=True)
    lighting = models.CharField(max_length=255, blank=True, null=True)
    camera_orientation = models.CharField(max_length=255, blank=True, null=True)
    editing = models.CharField(max_length=255, blank=True, null=True)
    script_breakdown = models.CharField(max_length=255, blank=True, null=True)

class ChecklistExtraItem(BaseModel):
    checklist = models.ForeignKey(ChecklistItem, on_delete=models.CASCADE, related_name='extra_checklist')
    text = models.TextField(blank=True, null=True, help_text="Value entered by talent")
    checklist_media = models.ImageField(upload_to="checklist_media/",null=True,blank=True)

class Script(BaseModel):
    proposal_version = models.ForeignKey(
            ProposalVersion, 
            on_delete=models.CASCADE, 
            related_name='scripts',
            null=True,
            blank=True
        )    
    content = models.TextField(null=True,blank=True)
    feedback = models.TextField(blank=True, null=True)
    sent_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='brand_scripts',null=True,blank=True)
    sent_by  = models.ForeignKey(User, on_delete=models.CASCADE, related_name='Talent_scripts',null=True,blank=True) 
    version = models.DecimalField(max_digits=4, decimal_places=1, default=Decimal("1.0"))
    status = models.IntegerField(choices=ScriptStatus.choices, default=ScriptStatus.SENT)
    accepted_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="accepted_scripts", null=True, blank=True)
    rejected_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="rejected_scripts", null=True, blank=True)
    terminated_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="terminated_scripts", null=True, blank=True)
    room = models.ForeignKey(Room,on_delete=models.CASCADE,null=True, blank=True)

class ContentUpload(BaseModel):
    """
    Content uploaded by talent for a specific deliverable item
    Supports the workflow: Talent uploads → Brand reviews → Approval/Rejection with feedback
    """
    script = models.ForeignKey(Script, on_delete=models.CASCADE, related_name='content_uploads')
    deliverable = models.ForeignKey(ProposalVersionDeliverableItem, on_delete=models.CASCADE, related_name='content_uploads')
    upload_content = models.JSONField(
        default=list, 
        blank=True, 
        null=True,
        help_text="List of up to 4 image/video URLs or file paths"
    ) 
    link = models.URLField(null=True, blank=True, help_text="External link to content if applicable")
    version = models.DecimalField(max_digits=4, decimal_places=1, default=Decimal("1.0"))
    status = models.IntegerField(choices=ContentUploadStatus.choices, default=ContentUploadStatus.SENT)
    feedback = models.TextField(blank=True, null=True, help_text="Feedback from brand for rejected content")
    
    # User tracking
    sent_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_content_uploads', null=True, blank=True)
    sent_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_content_uploads', null=True, blank=True)
    
    # Approval/Rejection tracking
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="approved_content_uploads", null=True, blank=True)
    rejected_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="rejected_content_uploads", null=True, blank=True)
    
    room = models.ForeignKey(Room, on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        verbose_name = 'Content Upload'
        verbose_name_plural = 'Content Uploads'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['script', '-version']),
            models.Index(fields=['deliverable', '-version']),
            models.Index(fields=['status']),
            models.Index(fields=['sent_by', '-created_at']),
            models.Index(fields=['sent_to', '-created_at']),
        ]

    def __str__(self):
        return f"Content v{self.version} for {self.deliverable} - {self.get_status_display()}"

    def save(self, *args, **kwargs):
        # Auto-increment version if this is a new content upload for the same deliverable
        if not self.pk:
            latest_version = ContentUpload.objects.filter(
                deliverable=self.deliverable,
                script=self.script
            ).order_by('-version').first()
            if latest_version:
                self.version = round(float(latest_version.version) + 0.1, 1)
        
        super().save(*args, **kwargs)

    @property
    def is_approved(self):
        return self.status == ContentUploadStatus.FINALISED

    @property
    def is_rejected(self):
        return self.status == ContentUploadStatus.REJECTED



    @property
    def content_count(self):
        """Returns the number of content items uploaded"""
        if self.upload_content:
            return len(self.upload_content)
        return 0

    def can_upload_more(self):
        """Check if more content can be uploaded (max 4 items)"""
        return self.content_count < 4


class ProductDelivery(BaseModel):
    """
    Product delivery workflow
    """
    proposal_version = models.ForeignKey(ProposalVersion, on_delete=models.CASCADE, related_name='product_deliveries')
    talent = models.ForeignKey(User, on_delete=models.CASCADE, related_name='product_deliveries_received')
    brand = models.ForeignKey(User, on_delete=models.CASCADE, related_name='product_deliveries_sent')
    
    # Core fields
    delivery_address = models.TextField(blank=True, null=True)
    expected_delivery_date = models.DateField(null=True, blank=True)
    receipt_status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('received', 'Received'),
        ('not_received', 'Not Received'),
    ], default='pending')
    
    # Timestamps
    last_receipt_check = models.DateTimeField(null=True, blank=True)
    next_check_allowed = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['proposal_version', 'talent']

    def __str__(self):
        return f"Delivery for {self.proposal_version} - {self.talent.email}"

    @property
    def can_confirm_receipt(self):
        """Check if talent can confirm receipt (24-hour cooldown)"""
        if self.receipt_status == 'received':
            return False
        if self.next_check_allowed:
            from django.utils import timezone
            return timezone.now() >= self.next_check_allowed
        return True

    def confirm_receipt(self, status):
        """Handle receipt confirmation"""
        from django.utils import timezone
        from datetime import timedelta
        
        self.receipt_status = status
        self.last_receipt_check = timezone.now()
        
        if status == 'not_received':
            self.next_check_allowed = timezone.now() + timedelta(hours=24)
        
        self.save()

class CampaignReview(BaseModel):
    """
    Stores feedback (rating and comment) from talent to brand and vice versa for a campaign.
    reviewer = the user giving the feedback
    reviewee = the user receiving the feedback (brand or talent)
    """
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='reviews')
    reviewer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='given_reviews')
    reviewee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_reviews')
    rating = models.PositiveSmallIntegerField()
    comment = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ('campaign', 'reviewer', 'reviewee')
        ordering = ['-created_at']

    def __str__(self):
        return f"Review by {self.reviewer.email} for {self.reviewee.email} on {self.campaign.campaign_name}"
