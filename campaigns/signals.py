# talent/signals.py

from django.db.models.signals import post_save,pre_save
from django.dispatch import receiver
from accounts.choices import UserTypeChoices
from campaigns.models import ProposalNegotiationStatus, ProposalVersion, Script, TalentCampaignApplication
from accounts.models import Notification, TalentProfile
from talent_trade.services.push_notification_service import NotificationHelper
from talent_trade.utils.choices import ApplicationStatusChoices, NotificationModuleChoice, ScriptStatus


@receiver(post_save, sender=TalentCampaignApplication)
def notify_brand_on_talent_application(sender, instance, created, **kwargs):
    if created and instance.status == ApplicationStatusChoices.APPLIED:
        campaign = instance.campaign
        brand_user = campaign.brand
        talent_profile = instance.talent
        talent_user = talent_profile.user

        talent_name = talent_profile.name or talent_user.get_full_name() or "A talent"
        message = f"{talent_name} has applied to your campaign '{campaign.campaign_name}'"


        notification = Notification.objects.create(
            user=brand_user,
            message=message,
            title=NotificationModuleChoice.TALENT_APPLIED,
            module = NotificationModuleChoice.TALENT_APPLIED,
            extra_info={
                "campaign_id": campaign.id,
                "talent_id": talent_profile.id,
                "application_id": instance.id,
            }
        )

        notification.push()


def _get_campaign_name_from_proposal(proposal_instance):
    """Helper to get campaign name from associated proposal."""
    # Assuming Proposal model has a ForeignKey to Campaign, e.g., proposal.campaign.name
    if hasattr(proposal_instance, 'campaign') and proposal_instance.campaign:
        return proposal_instance.campaign.campaign_name
    return "Unknown Campaign"

# Helper function to get brand name (can be put in utils or here)
def _get_brand_name_from_user(user_instance):
    """Helper to get brand name from a User."""
    try:
        if hasattr(user_instance, 'brand_profile'):
            return user_instance.brand_profile.brand_name
    except Exception: # Catching DoesNotExist or other potential errors
        pass
    return user_instance.username # Fallback to username if no brand profile

# Helper function to get talent name (adjust if you have a TalentProfile model)
def _get_talent_name_from_user(user_instance):
    """Helper to get talent name from a User."""
    # Assuming TalentProfile model is linked to User and has a 'name' field
    try:
        if hasattr(user_instance, 'talent_profile'):
            return user_instance.talent_profile.name # Adjust to your TalentProfile field
    except Exception:
        pass
    return user_instance.username # Fallback to username


@receiver(post_save, sender=ProposalVersion)
def proposal_version_status_changed(sender, instance, created, **kwargs):
    """
    Handles notification creation and push notification triggering
    when a ProposalVersion is saved or its status changes.
    """
    # Important: When updating an existing instance, 'created' is False.
    # To check if the status has actually changed, fetch the old instance.
    # We do this carefully to avoid fetching from DB if it's a brand new object.

    old_status = None
    if not created:
        try:
            # Get the previous state of the object from the database
            old_instance = sender.objects.get(pk=instance.pk)
            old_status = old_instance.status
        except sender.DoesNotExist:
            pass # Should not happen often if 'created' is False, but good to handle

    campaign_name = _get_campaign_name_from_proposal(instance.proposal)
    
    # --- Logic for SENT status ---
    if instance.status == ProposalNegotiationStatus.SENT and (created or old_status != ProposalNegotiationStatus.SENT):
        # This means a new ProposalVersion is being sent, or an existing one
        # has just changed its status to SENT.
        
        # Determine if it's an initial proposal or a counter-proposal
        # A counter-proposal means the version is > 1.0 (or > latest_version.version if latest was 1.0)
        is_counter_proposal = instance.version > 1.0

        # Notification for sent_to user (Talent/Brand)
        if instance.sent_to.user_type == UserTypeChoices.TALENT.value[0]:
            # Talent receives a proposal/counter-proposal from a Brand
            brand_name = _get_brand_name_from_user(instance.sent_by)
            
            if is_counter_proposal:
                title = NotificationModuleChoice.BRAND_SEND_REVISED_PROPOSAL
                message = f"{brand_name} has sent you a revised proposal for {campaign_name}. Review and take action."
                module = NotificationModuleChoice.BRAND_SEND_REVISED_PROPOSAL # Use the specific choice
            else: # Initial proposal
                title = NotificationModuleChoice.RECEIVED_PROPOSAL
                message = f"{brand_name} has sent you a proposal for {campaign_name}. Review and take action."
                module = NotificationModuleChoice.RECEIVED_PROPOSAL
            
            # Create DB notification for sent_to (Talent)
            notification = Notification.objects.create(
                user=instance.sent_to,
                message=message,
                title=title,
                module=module, # Or specific module name
                extra_info={"campaign_id": instance.proposal.campaign.id if instance.proposal.campaign else None,
                            "proposal_id": instance.proposal.id,
                            "proposal_version_id": instance.id,
                            "is_counter": is_counter_proposal},
            )
            # Trigger push notification
            if not notification.is_triggered: # Only trigger if not already triggered by DB save hook
                notification.push()
                

        elif instance.sent_to.user_type == UserTypeChoices.BRAND.value[0]:            # Brand receives a counter-proposal from a Talent
            # Brands don't receive initial "sent proposal" notifications as they are the ones sending it.
            if is_counter_proposal:
                talent_name = _get_talent_name_from_user(instance.sent_by)
                message = f"{talent_name} has sent you a counter proposal for {campaign_name}. Review and take action."
                module = NotificationModuleChoice.COUNTER_PROPOSAL_RECEIVED
                title = NotificationModuleChoice.COUNTER_PROPOSAL_RECEIVED
                # Create DB notification for sent_to (Brand)
                notification = Notification.objects.create(
                    user=instance.sent_to,
                    message=message,
                    module=module,
                    title=title,
                    extra_info={"campaign_id": instance.proposal.campaign.id if instance.proposal.campaign else None,
                                "proposal_id": instance.proposal.id,
                                "proposal_version_id": instance.id,
                                "is_counter": True},
                )
                # Trigger push notification
                if not notification.is_triggered:
                    notification.push()
                   

    # --- Logic for ACCEPTED status ---
    elif instance.status == ProposalNegotiationStatus.ACCEPTED and old_status != ProposalNegotiationStatus.ACCEPTED:
        # Proposal has just been accepted (status changed to ACCEPTED)
        
        # Determine if it was an initial proposal or a counter-proposal that was accepted
        is_initial_proposal_accepted = instance.version == 1.0 # This check assumes initial proposals are always 1.0

        # Notification for sent_by user (the one whose proposal/counter-proposal was accepted)
        if instance.sent_by.user_type == UserTypeChoices.TALENT.value[0]:
            # Talent's counter proposal was accepted by Brand
            brand_name = _get_brand_name_from_user(instance.sent_to) # The one who accepted is the brand
            message = f"Congratulations, {brand_name} has accepted your counter proposal for {campaign_name}"
            module  = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_BRAND
            title = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_BRAND
            
            notification = Notification.objects.create(
                user=instance.sent_by,
                message=message,
                module=module,
                title=title,
                extra_info={"campaign_id": instance.proposal.campaign.id if instance.proposal.campaign else None,
                            "proposal_id": instance.proposal.id,
                            "proposal_version_id": instance.id,
                            "was_counter": True},
            )
            if not notification.is_triggered:
                notification.push()
                

        elif instance.sent_by.user_type == UserTypeChoices.BRAND.value[0]:
            # Brand's proposal (initial or counter) was accepted by Talent
            talent_name = _get_talent_name_from_user(instance.sent_to) # The one who accepted is the talent

            if is_initial_proposal_accepted:
                 message = f"Congratulations, {talent_name} has accepted your proposal for {campaign_name}"
                 module = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_TALENT
                 title = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_TALENT
            else:
                 message = f"Congratulations, {talent_name} has accepted your counter proposal for {campaign_name}"
                 module = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_TALENT
                 title = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_TALENT

            notification = Notification.objects.create(
                user=instance.sent_by,
                message=message,
                module=module,
                title=title,
                extra_info={"campaign_id": instance.proposal.campaign.id if instance.proposal.campaign else None,
                            "proposal_id": instance.proposal.id,
                            "proposal_version_id": instance.id,
                            "was_counter": not is_initial_proposal_accepted},
            )
            if not notification.is_triggered:
                notification.push()
                



@receiver(post_save, sender=ProposalVersion)
def handle_proposal_version_acceptance(sender, instance, created, **kwargs):
    """
    Handle status updates when proposal version is created or updated.
    """
    proposal = instance.proposal
    sent_to_user = instance.sent_to

    # Only process if recipient is talent user
    if sent_to_user.user_type == UserTypeChoices.TALENT.value[0]:
        try:
            # Get the TalentProfile linked to this user
            talent_profile = TalentProfile.objects.get(user=sent_to_user)
        except TalentProfile.DoesNotExist:
            print(f"TalentProfile not found for user {sent_to_user.id}")
            return

        # Handle when proposal version is first created with SENT status
        if created and instance.status == ProposalNegotiationStatus.SENT:

            previous_sent_versions = ProposalVersion.objects.filter(
                proposal=proposal,
                sent_to=sent_to_user,
                status=ProposalNegotiationStatus.SENT
            ).exclude(id=instance.id)

            if not previous_sent_versions.exists():
                TalentCampaignApplication.objects.filter(
                    campaign=proposal.campaign,
                    talent=talent_profile
                ).update(status=ApplicationStatusChoices.ACTIVE)

        # Handle when proposal version status is updated to ACCEPTED
        if instance.status == ProposalNegotiationStatus.ACCEPTED:
            TalentCampaignApplication.objects.filter(
                campaign=proposal.campaign,
                talent=talent_profile
            ).update(status=ApplicationStatusChoices.ACCEPTED)



@receiver(post_save, sender=ProposalVersion)
def send_proposal_accept_notification(sender, instance, created, **kwargs):
    """
    Sends a notification when a proposal version is accepted.
    """

    old_status = getattr(instance, '_old_status', None)

    # Only act if status has *just* changed to ACCEPTED
    if instance.status != ProposalNegotiationStatus.ACCEPTED or old_status == ProposalNegotiationStatus.ACCEPTED:
        return

    proposal = instance.proposal
    sent_to_user = instance.sent_to
    sent_by_user = instance.sent_by

    if sent_to_user.user_type == UserTypeChoices.TALENT.value[0]:
        # Brand accepted the proposal, so notify the Talent
        brand_name = _get_brand_name_from_user(sent_by_user)
        message = f"Congratulations, {brand_name} has accepted your proposal for {proposal.campaign.campaign_name}"
        module = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_BRAND

    elif sent_to_user.user_type == UserTypeChoices.BRAND.value[0]:
        # Talent accepted the proposal, so notify the Brand
        talent_name = _get_talent_name_from_user(sent_by_user)
        message = f"Congratulations, {talent_name} has accepted your proposal for {proposal.campaign.campaign_name}"
        module = NotificationModuleChoice.PROPOSAL_ACCEPTED_BY_TALENT

    else:
        return  # Unknown user type

    # Create and push notification
    notification = Notification.objects.create(
        user=sent_to_user,
        message=message,
        module=module,
        extra_info={
            "campaign_id": proposal.campaign.id if proposal.campaign else None,
            "proposal_id": proposal.id,
            "proposal_version_id": instance.id,
            "was_counter": instance.version != 1.0,
        },
    )
    if not notification.is_triggered:
        notification.push()


@receiver(post_save, sender=Script)
def create_script_notification(sender, instance, created, **kwargs):
    """
    Create notification when a Script is created
    """
    if created:
        campaign_name = instance.proposal_version.proposal.campaign.campaign_name if instance.proposal_version and instance.proposal_version.proposal and instance.proposal_version.proposal.campaign else None

        if instance.sent_by.user_type == UserTypeChoices.BRAND.get_value("BRAND"):
            Notification.objects.create(
                user=instance.sent_to,
                module=NotificationModuleChoice.SCRIPT_SENT_BY_BRAND,
                title=NotificationModuleChoice.SCRIPT_SENT_BY_BRAND,
                message=f"Brand has sent you a script for review for campaign {campaign_name}.",
                extra_info={
                    "script_id": instance.id,
                    "version": str(instance.version),
                    "proposal_version_id": instance.proposal_version_id,
                    "campaign_name": campaign_name,
                },
            )

        elif instance.sent_by.user_type == UserTypeChoices.TALENT.get_value("TALENT"):
            talent_name = getattr(instance.sent_by.talent_profile, "name", instance.sent_by.get_full_name())
            Notification.objects.create(
                user=instance.sent_to,
                module=NotificationModuleChoice.SCRIPT_FEEDBACK_BY_TALENT,
                title=NotificationModuleChoice.SCRIPT_FEEDBACK_BY_TALENT,
                message=f"{talent_name} has shared feedback on the script for campaign {campaign_name}.",
                extra_info={
                    "script_id": instance.id,
                    "version": str(instance.version),
                    "proposal_version_id": instance.proposal_version_id,
                    "campaign_name": campaign_name,
                },
            )


@receiver(pre_save, sender=Script)
def handle_script_status_update(sender, instance, **kwargs):
    if not instance.pk:
        return  # New object

    previous = Script.objects.get(pk=instance.pk)
    if previous.status == instance.status:
        return

    actor = None
    message = ""
    module = None
    campaign_name = instance.proposal_version.proposal.campaign.campaign_name if instance.proposal_version and instance.proposal_version.proposal and instance.proposal_version.proposal.campaign else None

    if instance.status == ScriptStatus.FINALISED:
        actor = instance.accepted_by
        name = _get_user_name(actor)
        message = f"Script has been finalised by {name} for campaign {campaign_name}."
        module = NotificationModuleChoice.SCRIPT_FINALISED

    elif instance.status == ScriptStatus.TERMINATE:
        actor = instance.terminated_by
        name = _get_user_name(actor)
        message = f"Script has been terminated by {name} for campaign {campaign_name}."
        module = NotificationModuleChoice.SCRIPT_TERMINATED

    elif instance.status == ScriptStatus.REJECTED:
        actor = instance.rejected_by
        name = _get_user_name(actor)
        message = f"Script has been rejected by {name} for campaign {campaign_name}."
        module = NotificationModuleChoice.SCRIPT_REJECTED

    if actor and module:
        Notification.objects.create(
            user=instance.sent_to if actor == instance.sent_by else instance.sent_by,
            module=module,
            title=module,
            message=message,
            extra_info={
                "script_id": instance.id,
                "version": str(instance.version),
                "proposal_version_id": instance.proposal_version_id,
                "campaign_name": campaign_name,
            },
        )


def _get_user_name(user):
    if not user:
        return "Unknown"

    if user.user_type == UserTypeChoices.BRAND.get_value("BRAND"):
        return getattr(user.brand_profile, "brand_admin_name", user.get_full_name())
    elif user.user_type == UserTypeChoices.TALENT.get_value("TALENT"):
        return getattr(user.talent_profile, "name", user.get_full_name())
    return user.get_full_name()
