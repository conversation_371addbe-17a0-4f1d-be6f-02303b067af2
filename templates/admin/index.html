{% extends "admin/index.html" %}
{% load i18n static %}

{% block content %}
<!-- Dashboard Statistics Section -->
<div class="dashboard-stats" style="margin-bottom: 30px;">
    <div class="module">
        <h2>📊 Quick Statistics</h2>
        <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
            <div class="stat-card">
                <div class="stat-number">{{ total_talents|default:0 }}</div>
                <div class="stat-label">Total Talents</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_brands|default:0 }}</div>
                <div class="stat-label">Total Brands</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_campaigns_active|default:0 }}</div>
                <div class="stat-label">Active Campaigns</div>
            </div>
        </div>
        <div style="text-align: center;">
            <a href="{% url 'admin_dashboard_stats' %}" class="button" style="background: #417690; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">
                📈 View Detailed Statistics
            </a>
        </div>
    </div>
</div>

<style>
.dashboard-stats .module {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-stats .module h2 {
    margin: 0 0 20px 0;
    color: #333;
    text-align: center;
    font-size: 18px;
}

.stat-card {
    background: white;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #417690;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dashboard-stats .button:hover {
    background: #2c5aa0 !important;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}
</style>

<!-- Original Django Admin Content -->
{{ block.super }}
{% endblock %}
