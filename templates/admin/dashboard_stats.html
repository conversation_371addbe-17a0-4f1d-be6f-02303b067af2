{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">{{ site_header|default:_('Django administration') }}</a></h1>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>{{ title }}</h1>
    
    <div class="module" style="margin-bottom: 20px;">
        <h2>User Statistics</h2>
        <table>
            <tbody>
                <tr>
                    <th scope="row">
                        <a href="{% url 'admin:accounts_talentuser_changelist' %}">Total Talents</a>
                    </th>
                    <td>{{ stats.total_talents }}</td>
                </tr>
                <tr>
                    <th scope="row">
                        <a href="{% url 'admin:accounts_branduser_changelist' %}">Total Brands</a>
                    </th>
                    <td>{{ stats.total_brands }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="module" style="margin-bottom: 20px;">
        <h2>Paid Subscribers</h2>
        <table>
            <tbody>
                <tr>
                    <th scope="row">Total Paid Subscribers (Talent)</th>
                    <td>{{ stats.total_paid_subscribers_talent }}</td>
                </tr>
                <tr>
                    <th scope="row">Total Paid Subscribers (Brand)</th>
                    <td>{{ stats.total_paid_subscribers_brand }}</td>
                </tr>
            </tbody>
        </table>
        <p style="color: #666; font-size: 12px; margin-top: 10px;">
            <em>Note: Only active paid subscriptions (non-free) are counted.</em>
        </p>
    </div>

    <div class="module" style="margin-bottom: 20px;">
        <h2>Campaign Statistics</h2>
        <table>
            <tbody>
                <tr>
                    <th scope="row">
                        <a href="{% url 'admin:campaigns_campaign_changelist' %}?status=2">Total Campaigns (Active)</a>
                    </th>
                    <td>{{ stats.total_campaigns_active }}</td>
                </tr>
                <tr>
                    <th scope="row">
                        <a href="{% url 'admin:campaigns_campaign_changelist' %}?status=1">Total Campaigns (Draft)</a>
                    </th>
                    <td>{{ stats.total_campaigns_draft }}</td>
                </tr>
                <tr>
                    <th scope="row">
                        <a href="{% url 'admin:campaigns_campaign_changelist' %}?status=3">Total Campaigns (Archived)</a>
                    </th>
                    <td>{{ stats.total_campaigns_archived }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="module" style="margin-bottom: 20px;">
        <h2>App Analytics</h2>
        <table>
            <tbody>
                <tr>
                    <th scope="row">Total App Downloads</th>
                    <td>{{ stats.total_app_downloads }}</td>
                </tr>
            </tbody>
        </table>
        <p style="color: #666; font-size: 12px; margin-top: 10px;">
            <em>Note: App download statistics will be integrated with App Store/Play Store analytics.</em>
        </p>
    </div>

    <div style="margin-top: 30px;">
        <a href="{% url 'admin:index' %}" class="button">← Back to Admin Home</a>
    </div>
</div>

<style>
.dashboard .module {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.dashboard .module h2 {
    margin: 0 0 15px 0;
    padding: 0;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.dashboard .module table {
    width: 100%;
    border-collapse: collapse;
}

.dashboard .module table th {
    text-align: left;
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: normal;
    color: #666;
    width: 70%;
}

.dashboard .module table td {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: bold;
    color: #333;
    text-align: right;
}

.dashboard .module table th a {
    color: #417690;
    text-decoration: none;
}

.dashboard .module table th a:hover {
    color: #036;
    text-decoration: underline;
}

.dashboard .module table tr:last-child th,
.dashboard .module table tr:last-child td {
    border-bottom: none;
}
</style>
{% endblock %}
