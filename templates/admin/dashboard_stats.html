{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>{{ title }}</h1>
    
    <div class="module" style="margin-bottom: 20px;">
        <h2>📊 Platform Statistics</h2>
        <table style="width: 100%;">
            <tbody>
                <tr>
                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">
                        <a href="{% url 'admin:accounts_talentuser_changelist' %}" style="color: #417690; text-decoration: none;">Total Talents</a>
                    </th>
                    <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">{{ stats.total_talents }}</td>
                </tr>
                <tr>
                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">
                        <a href="{% url 'admin:accounts_branduser_changelist' %}" style="color: #417690; text-decoration: none;">Total Brands</a>
                    </th>
                    <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">{{ stats.total_brands }}</td>
                </tr>
                <tr>
                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">Total Paid Subscribers (Talent)</th>
                    <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">{{ stats.total_paid_subscribers_talent }}</td>
                </tr>
                <tr>
                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">Total Paid Subscribers (Brand)</th>
                    <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">{{ stats.total_paid_subscribers_brand }}</td>
                </tr>
                <tr>
                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">
                        <a href="{% url 'admin:campaigns_campaign_changelist' %}?status=2" style="color: #417690; text-decoration: none;">Total Campaigns (Active)</a>
                    </th>
                    <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">{{ stats.total_campaigns_active }}</td>
                </tr>
                <tr>
                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">
                        <a href="{% url 'admin:campaigns_campaign_changelist' %}?status=1" style="color: #417690; text-decoration: none;">Total Campaigns (Draft)</a>
                    </th>
                    <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">{{ stats.total_campaigns_draft }}</td>
                </tr>
                <tr>
                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">
                        <a href="{% url 'admin:campaigns_campaign_changelist' %}?status=3" style="color: #417690; text-decoration: none;">Total Campaigns (Archived)</a>
                    </th>
                    <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">{{ stats.total_campaigns_archived }}</td>
                </tr>
                <tr>
                    <th style="text-align: left; padding: 10px;">Total App Downloads</th>
                    <td style="text-align: right; padding: 10px; font-weight: bold;">{{ stats.total_app_downloads }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div style="margin-top: 30px;">
        <a href="{% url 'admin:index' %}" class="button">← Back to Admin Home</a>
    </div>
</div>

<style>
.dashboard .module {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.dashboard .module h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
}

.dashboard .module table {
    border-collapse: collapse;
}

.dashboard .module table th a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}
