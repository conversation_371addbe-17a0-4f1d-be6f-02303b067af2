"""talent_trade URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include

admin.sites.AdminSite.site_title = "Login To Talent Trade Portal"
admin.sites.AdminSite.site_header = "Talent Trade Administration"
admin.sites.AdminSite.index_title = "Welcome To Talent Trade Portal"

urlpatterns = [
    path('admin/', admin.site.urls),
    path('nested_admin/', include('nested_admin.urls')),
    path('accounts/', include('accounts.urls')),
    path('campaigns/', include('campaigns.urls')),
    path('chat/', include('chat.urls')),
]
