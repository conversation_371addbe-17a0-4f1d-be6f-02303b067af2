from django.db.models import Q
from campaigns.models import Campaign, CampaignPlatformSelection
from accounts.models import Projects
from accounts.models import SocialMediaPlatform
from talent_trade.utils.choices import CampaignStatusChoices


class MatchEngine:
    # Adjust weights as needed
    WEIGHTS = {
        'state': 1,
        'city': 1,
        'gender': 1,
        'languages': 1,
        'categories': 2,
        'platform': 1, 
    }

    @classmethod
    def compute_campaign_match(cls, user, campaign):
        score = 0
        total = sum(cls.WEIGHTS.values())

        profile = getattr(user, 'talent_profile', None)
        if not profile:
            return 0

        # State match
        if campaign.preferred_state and campaign.preferred_state == profile.state:
            score += cls.WEIGHTS['state']

        # City match
        if campaign.preferred_city and campaign.preferred_city == profile.city:
            score += cls.WEIGHTS['city']

        # Gender match
        if campaign.preferred_gender and campaign.preferred_gender == profile.gender:
            score += cls.WEIGHTS['gender']

        # Language match
        if campaign.preferred_languages:
            user_languages = set(profile.languages.values_list('name', flat=True))
            if user_languages.intersection(set(campaign.preferred_languages)):
                score += cls.WEIGHTS['languages']

        # Category match
        if campaign.category.exists():
            user_categories = profile.expertise_areas.values_list('id', flat=True)
            if campaign.category.filter(id__in=user_categories).exists():
                score += cls.WEIGHTS['categories']

        # Platform match
        platform_score = cls.compare_platform_with_social_tags(user, campaign)
        if platform_score >= 50:  # Optional: threshold to count as a match
            score += cls.WEIGHTS['platform']

        return round((score / total) * 100, 2)

    @classmethod
    def compare_platform_with_social_tags(cls, user, campaign):
        """
        Compares campaign platforms with the user's social_tags from projects.
        Returns match % based on overlap.
        """
        campaign_platform_ids = set(
            CampaignPlatformSelection.objects.filter(
                campaign=campaign
            ).values_list('platform_id', flat=True)
        )

        user_platform_ids = set(
            SocialMediaPlatform.objects.filter(
                projects__user=user
            ).values_list('id', flat=True)
        )

        if not campaign_platform_ids:
            return 0.0

        matched = campaign_platform_ids & user_platform_ids
        return round((len(matched) / len(campaign_platform_ids)) * 100, 2)

    @classmethod
    def get_user_campaign_score(cls, user, brand):
        """
        Computes best and average match score across all campaigns of a brand.
        """
        campaigns = Campaign.objects.filter(
            brand=brand
        ).exclude(status=CampaignStatusChoices.DRAFT)

        if not campaigns.exists():
            return {'best_match': 0.0, 'average_match': 0.0}

        match_scores = [cls.compute_campaign_match(user, campaign) for campaign in campaigns]

        return {
            "best_match": max(match_scores),
            "average_match": round(sum(match_scores) / len(match_scores), 2)
        }
