import json
import logging
import requests
from rest_framework import status
from django.utils import timezone
from google.auth.transport.requests import Request
from google.oauth2 import service_account

from talent_trade import settings

class NotificationHelper:
    def __init__(self, user, message, module=None, extra_info=None, created_at=None,title=None):
        self.user = user
        self.message = message
        self.module = module
        self.extra_info = extra_info or {}
        self.created_at = created_at
        self.title = title or ""
        self.tokens = self.get_user_tokens()

    # def get_user_tokens(self):
    #     from accounts.models import Token  # <-- imported here to avoid circular import
    #     tokens = Token.objects.filter(user=self.user).values_list("user__id", "user__email", "registration_id")
    #     for user_id, email, reg_id in tokens:
    #     return [reg_id for _, _, reg_id in tokens]

    def get_user_tokens(self):
        from accounts.models import Token  # <-- imported here to avoid circular import
        return list(Token.objects.filter(user=self.user).values_list("registration_id", flat=True))

    def get_access_token(self):
        SERVICE_ACCOUNT_FILE = settings.GOOGLE_APPLICATION_CREDENTIALS_PATH
        SCOPES = ["https://www.googleapis.com/auth/firebase.messaging"]
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES
        )
        request = Request()
        credentials.refresh(request)
        return credentials.token

    def push(self):
        success = False  

        if not self.tokens:
            return success

        access_token = self.get_access_token()
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json; UTF-8",
        }

        for token in self.tokens:
            payload = {
                "message": {
                    "token": token,
                    "notification": {
                        "title": self.title,
                        "body": self.message,
                    },
                    "data": {
                        "title": self.title, 
                        "module": str(self.module or ""),
                        "extra_info": json.dumps(self.extra_info),
                        "created_at": str(self.created_at) if self.created_at else "",
                    },
                    "android": {
                        "priority": "high",
                        "notification": {
                            "sound": "default",
                        },
                    },
                    "apns": {
                        "payload": {
                            "aps": {
                                "sound": "default",
                                "content-available": 1,
                                "alert": {
                                    "title": self.title,
                                    "body": self.message,
                                },
                            },
                        },
                    },
                }
            }

            response = requests.post(settings.FCM_URL, headers=headers, json=payload)

            if response.status_code == status.HTTP_200_OK:
                success = True  
                logging.info(
                    "[%s] Notification sent successfully: %s",
                    timezone.localtime(timezone.now()), response.status_code,
                )
            else:
                logging.error(
                    "[%s] Notification error response: %s",
                    timezone.localtime(timezone.now()), response.content.decode("utf-8"),
                )

        return success 
