# core/services/email_service.py

from django.core.mail import send_mail
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class EmailService:
    @staticmethod
    def send_email(subject, message, recipient_list, fail_silently=True):
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=recipient_list,
                fail_silently=fail_silently
            )
            return True, None
        except Exception as e:
            logger.error(f"[EmailService] Failed to send email to {recipient_list}: {e}")
            return False, str(e)
