import requests
from django.conf import settings
from requests.exceptions import Timeout, RequestException
from accounts.models import AadhaarClient, PANVerification
from talent_trade.utils.choices import KYCStatusChoices


def generate_aadhar_otp(aadhar_number):
    url = f"{settings.SUREPASS_BASE_URL}/api/v1/aadhaar-v2/generate-otp"
    headers = {
        "Authorization": f"Bearer {settings.SUREPASS_AUTH_TOKEN}", 
        "Content-Type": "application/json"
    }
    payload = {"id_number": str(aadhar_number)}

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=settings.SUREPASS_DEFAULT_TIMEOUT)
        response.raise_for_status()
        return {
            "success": True,
            "data": response.json()
        }
    
    except ConnectionError as ce:
        return {
            "status": False,
            "message": "Network connection error while contacting Surepass: " + str(ce),
        }
    
    except Timeout:
        return {
            "success": False,
            "type": "timeout",  # distinguish timeout error
            "message": "Server is taking longer to respond. Please try again later."
        }

    except requests.exceptions.RequestException as e:
        status_code = None
        raw_response = None
        surepass_message = str(e)

        response_obj = getattr(e, 'response', None)

        if response_obj is not None:
            status_code = response_obj.status_code

            try:
                raw_response = response_obj.json()
                surepass_message = raw_response.get("message", surepass_message)

                if surepass_message == "Backend Timed Out. Try Again.":
                    surepass_message = "Server is taking longer to respond. Please try again later."
            except ValueError:
                raw_response = response_obj.text

        return {
            "success": False,
            "status_code": status_code,
            "message": surepass_message,
            "raw_response": raw_response
        }



def verify_aadhar_otp(client_id, otp):
    url = f"{settings.SUREPASS_BASE_URL}/api/v1/aadhaar-v2/submit-otp"
    headers = {
        "Authorization": f"Bearer {settings.SUREPASS_AUTH_TOKEN}",
        "Content-Type": "application/json"
    }
    payload = {"client_id": client_id, "otp": otp}

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=settings.SUREPASS_DEFAULT_TIMEOUT)
        response.raise_for_status()
        return response.json()
    
    except Timeout:
        return {
            "success": False,
            "message": "Server is taking longer to respond. Please try again later.",
            "raw_response": None
        }
    
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "message": f"Request failed: {e}",
            "raw_response": getattr(e.response, "text", None)
        }
    except ValueError:
        return {
            "success": False,
            "message": "Invalid JSON response",
            "raw_response": response.text if 'response' in locals() else None
        }
    

def verify_pan(id_number: str):
    url = f"{settings.SUREPASS_BASE_URL}/api/v1/pan/pan-comprehensive"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {settings.SUREPASS_AUTH_TOKEN}"
    }
    payload = {"id_number": id_number}

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=settings.SUREPASS_DEFAULT_TIMEOUT)
        response.raise_for_status()
        return response.json()
    
    except Timeout:
        return {
            "success": False,
            "message": "Server is taking longer to respond. Please try again later.",
            "raw_response": None
        }
    
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "message": f"Request failed: {e}",
            "raw_response": getattr(e.response, "text", None)
        }
    except ValueError:
        return {
            "success": False,
            "message": "Invalid JSON response",
            "raw_response": response.text if 'response' in locals() else None
        }


def update_kyc_status(user):
    aadhaar_verified = AadhaarClient.objects.filter(user=user, is_aadhar_verified=True).exists()
    pan_verified = PANVerification.objects.filter(user=user, is_pan_verified=True).exists()

    if aadhaar_verified and pan_verified:
        new_status = KYCStatusChoices.VERIFIED
    elif aadhaar_verified or pan_verified:
        new_status = KYCStatusChoices.IN_PROGRESS
    else:
        new_status = KYCStatusChoices.NOT_STARTED

    if user.kyc_status != new_status:
        user.kyc_status = new_status
        user.save(update_fields=["kyc_status"])
