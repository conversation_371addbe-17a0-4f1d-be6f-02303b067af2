"""
Django settings for talent_trade project.

Generated by 'django-admin startproject' using Django 3.2.12.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

import os
from pathlib import Path
from corsheaders.defaults import default_headers, default_methods
from dotenv import load_dotenv
load_dotenv()



# Build paths inside the project like this: BASE_DIR / 'subdir'.

BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-(_bsoh1p$ea3+(q7^4#g4$@)f9_e0-!580^^$*1hv8b4y*k3ga'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

AUTH_USER_MODEL = 'accounts.User'

# Application definition

INSTALLED_APPS = [
    'jazzmin',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'rest_framework',
    # 'rest_framework.authtoken',
    'accounts',
    'campaigns',
    'chat',
    'nested_admin',
    'channels',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'talent_trade.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / "templates"],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'talent_trade.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.getenv("DATABASE_NAME"),
        "USER": os.getenv("DATABASE_USER"),
        "PASSWORD": os.getenv("DATABASE_PASSWORD"),
        "HOST": os.getenv("DATABASE_HOST"),
        "PORT": os.getenv("DATABASE_PORT"),
        "ATOMIC_REQUESTS": True,
    }
}

IS_DEBUG_TOOLBAR_ACTIVE = os.getenv("DEBUG_TOOLBAR_ACTIVE", default=False)

if IS_DEBUG_TOOLBAR_ACTIVE:
    INTERNAL_IPS = [
        "127.0.0.1",
    ]
    INSTALLED_APPS += ("debug_toolbar",)
    MIDDLEWARE += ["debug_toolbar.middleware.DebugToolbarMiddleware"]


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

CORS_ALLOW_METHODS = (*default_methods,)
CORS_ALLOW_HEADERS = (
    *default_headers,
    "access-control-allow-origin",
)

CORS_ALLOW_ALL_ORIGINS = True


CORS_ALLOW_CREDENTIALS = True


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Whitenoise configuration
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
WHITENOISE_USE_FINDERS = True
WHITENOISE_MANIFEST_STRICT = False
WHITENOISE_ALLOW_ALL_ORIGINS = True

# AWS S3 SETTINGS
AWS_S3_BUCKET_NAME = os.getenv('AWS_S3_BUCKET_NAME')
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME')
AWS_S3_CUSTOM_DOMAIN = f"https://{AWS_S3_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com"
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")



# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# JWT Settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'talent_trade.middleware.authentication.TokenAuthentication',
    ),
    "DEFAULT_FILTER_BACKENDS": (
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.OrderingFilter",
        "rest_framework.filters.SearchFilter",
    ),
    
}

CLOUDFRONT_DOMAIN = os.getenv("CLOUDFRONT_DOMAIN",default=None)

EMAIL_BACKEND = os.getenv('EMAIL_BACKEND')
EMAIL_HOST = os.getenv('EMAIL_HOST')
EMAIL_PORT = os.getenv('EMAIL_PORT',)
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS',)
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL')


YOUTUBE_API_KEY = 'AIzaSyCNYAunQAMt1ZQedr_qJ5O23kaurkQpvHM'  # Replace with your actual API key
SUREPASS_AUTH_TOKEN = os.getenv("SUREPASS_PROD_AUTH_TOKEN") or os.getenv("SUREPASS_SANDBOX_AUTH_TOKEN")
SUREPASS_BASE_URL = os.getenv("SUREPASS_PROD_BASE_URL") or os.getenv("SUREPASS_SANDBOX_BASE_URL")


GOOGLE_APPLICATION_CREDENTIALS = os.getenv(
    "GOOGLE_APPLICATION_CREDENTIALS_PATH", default="service_data.json"
)
FCM_URL = os.getenv("FCM_URL")
GOOGLE_APPLICATION_CREDENTIALS_PATH = os.path.join(
    BASE_DIR, GOOGLE_APPLICATION_CREDENTIALS
)

SUREPASS_DEFAULT_TIMEOUT = int(os.getenv("SUREPASS_DEFAULT_TIMEOUT", 30))


JAZZMIN_SETTINGS = {
    "site_title" :"TalentTrade Admin",
    "site_header" :"TalentTrade Admin Panel",
    "index_title" : "TalentTrade Admin Dashboard",
    "welcome_sign": "Welcome to TalentTrade",
    "copyright": "TalentTrade © 2025",
    "show_sidebar": True,
    "navigation_expanded": True,
    "icons": {
        "auth": "fas fa-users-cog",
        "auth.User": "fas fa-user",
        "auth.Group": "fas fa-users",
        "orders.Order": "fas fa-shopping-cart",
        "logistics.LogisticsStatus": "fas fa-shipping-fast",
    },
    "custom_css": "",
    "custom_js": "",
    
    # Logo settings
    # "site_logo": ,
    # "site_icon": "images/40.png", 
    # "site_brand": "Wanderobe Admin",
    
    
}

# Channels Configuration
ASGI_APPLICATION = 'talent_trade.asgi.application'

# Channel Layers Configuration
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [('redis', 6379)],
        },
    },
}
