from django.db import models


class S3FilePathChoices(models.TextChoices):
    """S3 file path choices for jpg files."""

    PROFILE_PICTURE = "profile_picture", "profile_pics/"
    PROJECT_IMAGE = "project_images","project_docs/images/"
    PROJECT_VIDEO = "project_videos" , "project_docs/videos/"
    PROJECT_VIDEO_THUMBNAIL = "project_video_thumbnail", "project_docs/thumbnails/"
    CIN_CERTIFICATE = "cin_certificate" , "cin_certificates/"
    BRAND_LOGO = "brand_logo" , "brand_logos/"
    CAMPAIGN_BANNER = "campaign_banner" , "campaign_banners/"
    BARTER_PRODUCT_IMAGE = "barter_product_image", "barter_product_images/"


class CampaignStatusChoices(models.IntegerChoices):
    DRAFT = 1, 'Draft'
    ACTIVE = 2, 'Active'
    ARCHIVED = 3, 'Archived'


class KYCStatusChoices(models.IntegerChoices):

    NOT_STARTED = 0, 'Not Started'
    IN_PROGRESS = 1, 'In Progress'
    VERIFIED = 2, 'Verified'


class ApplicationStatusChoices(models.IntegerChoices):
        APPLIED = 1, 'Applied'
        ACTIVE = 2, 'Active'
        ACCEPTED = 3, 'Accepted'
        REJECTED = 4, 'Rejected'
        UNAPPLIED = 5, 'Unapplied'
        

class NotificationModuleChoice(models.IntegerChoices):
    TALENT_APPLIED = 1, "Talent Applied"
    RECEIVED_PROPOSAL = 2, "Talent Received Proposal"
    BRAND_SEND_REVISED_PROPOSAL = 3, "Brand Send Revised Proposal"
    COUNTER_PROPOSAL_RECEIVED = 4, "Talent Counter Proposal Received"
    PROPOSAL_ACCEPTED_BY_BRAND = 5, "Proposal Accepted By Brand"
    PROPOSAL_ACCEPTED_BY_TALENT = 6, "Proposal Accepted By Talent"

    # --- Script Related Notifications ---
    SCRIPT_SENT_BY_BRAND = 7, "Script Sent by Brand"
    SCRIPT_RECEIVED_BY_TALENT = 8, "Script Received by Talent"
    SCRIPT_REVISED_BY_BRAND = 9, "Script Revised by Brand"
    SCRIPT_FEEDBACK_BY_TALENT = 10, "Feedback Sent by Talent"
    SCRIPT_FINALISED = 11, "Script Finalised"
    SCRIPT_TERMINATED = 12, "Script Terminated"

    # --- Agreement Notifications ---
    AGREEMENT_SENT_BY_BRAND = 13, "Agreement Sent by Brand"
    AGREEMENT_FEEDBACK_BY_TALENT = 14, "Agreement Feedback by Talent"
    AGREEMENT_ACCEPTED = 15, "Agreement Accepted"
    AGREEMENT_REJECTED = 16, "Agreement Rejected"
    AGREEMENT_TERMINATED = 17, "Agreement Terminated"


class CampaignTypeChoices(models.TextChoices):
    PAID = "paid", "Paid"
    BARTER = "barter", "Barter"

    
class ScriptStatus(models.IntegerChoices):
    DRAFT = 0, "Draft"
    SENT = 1, "Sent"
    FINALISED = 2, "Finalised"
    TERMINATE = 3, "Terminate"
    REJECTED = 4, "Rejected"

class ContentUploadStatus(models.IntegerChoices):
    SENT = 1, "Sent"
    FINALISED = 2, "Finalised"
    TERMINATE = 3, "Terminate"
    REJECTED = 4, "Rejected"


class AgreementStatusChoices(models.IntegerChoices):
    SENT = 1, 'Sent'
    ACCEPTED = 2, 'Accepted'
    REJECTED = 3, 'Rejected'
    TERMINATED = 4, 'Terminated'