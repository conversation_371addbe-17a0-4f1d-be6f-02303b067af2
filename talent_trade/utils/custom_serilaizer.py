from django.db import IntegrityError
from rest_framework import serializers


class OptionChoiceField(serializers.ChoiceField):
    """all option select choice field"""

    def to_representation(self, value):
        if not value and self.allow_blank:
            return value
        return self._choices[value]

    def to_internal_value(self, data):
        # To support inserts with the value
        if not data and self.allow_blank:
            return ""
        for key, val in self._choices.items():
            if data in [key, val]:
                return key
        self.fail("invalid_choice", input=data)
        return None