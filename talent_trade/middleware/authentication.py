from rest_framework.authentication import TokenAuthentication as TokenAuth
from rest_framework.exceptions import AuthenticationFailed
from django.utils import timezone
from datetime import datetime
from accounts.models import Token
import logging

class TokenAuthentication(TokenAuth):
    """Authenticate token credentials and support multi-device token handling."""

    keyword = "Token"
    logger = logging.getLogger(__name__)

    def authenticate_credentials(self, key):
        """Validate the token key from DB and return associated user/token."""
        try:
            token = Token.objects.select_related('user').get(key=key)
            self.logger.info(f"[{datetime.now()}] Token authenticated for user: {token.user.id}")
        except Token.DoesNotExist:
            raise AuthenticationFailed("Invalid token")
        return (token.user, token)

    def get_or_create_auth_token(self, user, request):
        """Get or recreate token per (user, device_id) pair and update metadata."""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        device_id = request.data.get("device_id")
        registration_id = request.data.get("player_id", "")
        device_name = request.data.get("device_name", "")
        device_os = request.data.get("device_os", "")

        if not device_id:
            raise AuthenticationFailed("Device ID is required")

        # Remove existing token if one exists for the same user + device_id
        existing_token = Token.objects.filter(user=user, device_id=device_id).first()
        if existing_token:
            self.logger.info(f"[{current_time}] Deleting existing token for user: {user.id}, device_id: {device_id}")
            existing_token.delete()

        # Create new token
        token = Token.objects.create(
            user=user,
            device_id=device_id,
            registration_id=registration_id,
            device_name=device_name,
            device_os=device_os
        )

        # Update user's last login time
        user.last_login = timezone.now()
        user.save(update_fields=["last_login"])

        self.logger.info(f"[{current_time}] New token created for user: {user.id}, device_id: {device_id}")
        return token

    def authenticate(self, request):
        """Check Authorization header format and authenticate token."""
        auth = request.headers.get("Authorization", "")
        if auth.startswith(self.keyword + " "):
            try:
                key = auth.split()[1]
            except IndexError:
                raise AuthenticationFailed("Invalid token format")
            return self.authenticate_credentials(key)
        return None
