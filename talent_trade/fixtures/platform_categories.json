[{"model": "accounts.platformcategory", "pk": 1, "fields": {"platform": 1, "name": "Images"}}, {"model": "accounts.platformcategory", "pk": 2, "fields": {"platform": 1, "name": "Videos"}}, {"model": "accounts.platformcategory", "pk": 3, "fields": {"platform": 1, "name": "Live"}}, {"model": "accounts.platformcategory", "pk": 4, "fields": {"platform": 1, "name": "Stories"}}, {"model": "accounts.platformcategory", "pk": 5, "fields": {"platform": 1, "name": "Comments"}}, {"model": "accounts.platformcategory", "pk": 6, "fields": {"platform": 1, "name": "<PERSON>s"}}, {"model": "accounts.platformcategory", "pk": 7, "fields": {"platform": 1, "name": "<PERSON><PERSON>"}}, {"model": "accounts.platformcategory", "pk": 8, "fields": {"platform": 2, "name": "Static Images"}}, {"model": "accounts.platformcategory", "pk": 9, "fields": {"platform": 2, "name": "Carousels"}}, {"model": "accounts.platformcategory", "pk": 10, "fields": {"platform": 2, "name": "Videos"}}, {"model": "accounts.platformcategory", "pk": 11, "fields": {"platform": 2, "name": "Stories"}}, {"model": "accounts.platformcategory", "pk": 12, "fields": {"platform": 2, "name": "<PERSON><PERSON>"}}, {"model": "accounts.platformcategory", "pk": 13, "fields": {"platform": 2, "name": "Live"}}, {"model": "accounts.platformcategory", "pk": 14, "fields": {"platform": 2, "name": "Repost (Story)"}}, {"model": "accounts.platformcategory", "pk": 15, "fields": {"platform": 2, "name": "Repost/Tag (Post)"}}, {"model": "accounts.platformcategory", "pk": 16, "fields": {"platform": 2, "name": "Comments"}}, {"model": "accounts.platformcategory", "pk": 17, "fields": {"platform": 2, "name": "<PERSON>s"}}, {"model": "accounts.platformcategory", "pk": 18, "fields": {"platform": 3, "name": "Static Images"}}, {"model": "accounts.platformcategory", "pk": 19, "fields": {"platform": 3, "name": "Videos"}}, {"model": "accounts.platformcategory", "pk": 20, "fields": {"platform": 3, "name": "Comments"}}, {"model": "accounts.platformcategory", "pk": 21, "fields": {"platform": 3, "name": "<PERSON>s"}}, {"model": "accounts.platformcategory", "pk": 22, "fields": {"platform": 4, "name": "Tweets"}}, {"model": "accounts.platformcategory", "pk": 23, "fields": {"platform": 4, "name": "Images/GIFs"}}, {"model": "accounts.platformcategory", "pk": 24, "fields": {"platform": 4, "name": "Videos"}}, {"model": "accounts.platformcategory", "pk": 25, "fields": {"platform": 4, "name": "Threads"}}, {"model": "accounts.platformcategory", "pk": 26, "fields": {"platform": 4, "name": "Retweets"}}, {"model": "accounts.platformcategory", "pk": 27, "fields": {"platform": 4, "name": "Comments"}}, {"model": "accounts.platformcategory", "pk": 28, "fields": {"platform": 4, "name": "<PERSON>s"}}, {"model": "accounts.platformcategory", "pk": 29, "fields": {"platform": 5, "name": "Videos"}}, {"model": "accounts.platformcategory", "pk": 30, "fields": {"platform": 5, "name": "Live Streams"}}, {"model": "accounts.platformcategory", "pk": 31, "fields": {"platform": 5, "name": "Shorts"}}, {"model": "accounts.platformcategory", "pk": 32, "fields": {"platform": 5, "name": "Comments"}}, {"model": "accounts.platformcategory", "pk": 33, "fields": {"platform": 5, "name": "<PERSON>s"}}]